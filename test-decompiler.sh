#!/bin/bash

echo "=== JAR Decompiler Service Test ==="
echo ""

# Test health endpoint
echo "1. Testing health endpoint..."
curl -s http://localhost:8080/api/health
echo ""
echo ""

# Test file upload and decompilation
echo "2. Testing JAR decompilation..."
echo "Uploading test-jar-1.0.0.jar..."

# Use curl to upload the JAR file
curl -X POST \
  -F "jarFile=@test-jar/target/test-jar-1.0.0.jar" \
  http://localhost:8080/upload \
  -o decompile_result.html

echo "Decompilation result saved to decompile_result.html"
echo ""

# Check if the result file was created
if [ -f "decompile_result.html" ]; then
    echo "✅ Decompilation completed successfully!"
    echo "📄 Result file size: $(du -h decompile_result.html | cut -f1)"
else
    echo "❌ Decompilation failed!"
fi

echo ""
echo "=== Test Complete ==="
echo ""
echo "You can:"
echo "1. Open http://localhost:8080 in your browser to use the web interface"
echo "2. Upload the test JAR file: test-jar/target/test-jar-1.0.0.jar"
echo "3. View the decompiled source code structure"
echo "4. Download the complete project as a ZIP file"
