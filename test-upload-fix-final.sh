#!/bin/bash

echo "🔧 最终文件上传修复验证"
echo "======================="
echo ""

# 检查服务状态
echo "1. 检查服务状态..."
response=$(curl -s http://localhost:8080/api/health)
if [ "$response" = "JAR Decompiler Service is running" ]; then
    echo "✅ 服务正常运行"
else
    echo "❌ 服务未运行，请先启动服务"
    exit 1
fi
echo ""

# 检查容器配置
echo "2. 检查容器JVM参数..."
echo ""

docker_cmd=$(docker inspect jar-decompiler-service --format='{{.Config.Env}}' | grep JAVA_OPTS)
echo "📋 当前JAVA_OPTS配置:"
echo "$docker_cmd"
echo ""

if echo "$docker_cmd" | grep -q "spring.servlet.multipart.max-file-size=524288000"; then
    echo "✅ JVM参数包含文件大小限制配置"
else
    echo "❌ JVM参数未包含文件大小限制配置"
fi
echo ""

# 检查容器运行状态
echo "3. 检查容器运行状态..."
echo ""

container_status=$(docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep jar-decompiler-service)
echo "📋 容器状态:"
echo "$container_status"
echo ""

# 显示修复方案
echo "📋 当前修复方案:"
echo "================"
echo ""
echo "✅ 使用JVM系统属性强制覆盖配置:"
echo "1. -Dspring.servlet.multipart.max-file-size=524288000 (500MB)"
echo "2. -Dspring.servlet.multipart.max-request-size=524288000 (500MB)"
echo "3. -Dserver.tomcat.max-swallow-size=524288000 (500MB)"
echo "4. -Dserver.tomcat.max-http-form-post-size=524288000 (500MB)"
echo "5. -Dspring.servlet.multipart.enabled=true"
echo ""

# 显示测试建议
echo "🧪 测试建议:"
echo "============"
echo ""
echo "1. 测试小文件 (< 200MB):"
echo "   - 应该正常上传和处理"
echo ""
echo "2. 测试中等文件 (200-400MB):"
echo "   - 应该正常上传和处理"
echo ""
echo "3. 测试大文件 (400-500MB):"
echo "   - 应该正常上传和处理"
echo ""
echo "4. 测试超大文件 (> 500MB):"
echo "   - 应该被拒绝，显示友好错误信息"
echo ""

# 显示部署命令
echo "📦 完整部署命令:"
echo "==============="
echo ""
echo "docker run -d \\"
echo "    --name jar-decompiler-service \\"
echo "    --restart unless-stopped \\"
echo "    -p 8080:8080 \\"
echo "    -e SPRING_PROFILES_ACTIVE=docker \\"
echo "    -e TZ=Asia/Shanghai \\"
echo "    -e LANG=C.UTF-8 \\"
echo "    -e LC_ALL=C.UTF-8 \\"
echo "    -e JAVA_OPTS=\"-Xmx2g -Xms1g -XX:+UseG1GC -XX:+UseContainerSupport -Dfile.encoding=UTF-8 -Dsun.jnu.encoding=UTF-8 -Dspring.servlet.multipart.max-file-size=524288000 -Dspring.servlet.multipart.max-request-size=524288000 -Dserver.tomcat.max-swallow-size=524288000 -Dserver.tomcat.max-http-form-post-size=524288000 -Dspring.servlet.multipart.enabled=true\" \\"
echo "    jar-decompiler-service:2.1.0"
echo ""

# 显示访问信息
echo "🌐 访问信息:"
echo "==========="
echo ""
echo "- Web界面: http://localhost:8080"
echo "- 健康检查: http://localhost:8080/api/health"
echo "- 最大文件大小: 500MB (524,288,000 bytes)"
echo ""

# 显示日志查看命令
echo "🔍 故障排除:"
echo "==========="
echo ""
echo "查看实时日志:"
echo "docker logs -f jar-decompiler-service"
echo ""
echo "查看最近日志:"
echo "docker logs --tail 50 jar-decompiler-service"
echo ""
echo "检查容器配置:"
echo "docker inspect jar-decompiler-service"
echo ""

echo "✅ 文件上传限制修复完成！"
echo ""
echo "🎯 现在可以上传最大500MB的JAR文件了。"
echo "如果仍然遇到问题，请检查："
echo "1. 文件确实小于500MB"
echo "2. 网络连接稳定"
echo "3. 浏览器没有自己的上传限制"
echo "4. 服务器磁盘空间充足"
