#!/bin/bash

echo "🧪 完整测试Unicode转义修复效果"
echo "=============================="
echo ""

# 检查服务状态
echo "1. 检查服务状态..."
if curl -s http://localhost:8080/api/health | grep -q "running"; then
    echo "✅ 服务正常运行"
else
    echo "❌ 服务未运行"
    exit 1
fi
echo ""

# 检查容器状态
echo "2. 检查容器状态..."
container_status=$(docker ps --format "table {{.Names}}\t{{.Status}}" | grep jar-decompiler-service)
if [ -n "$container_status" ]; then
    echo "✅ 容器状态: $container_status"
else
    echo "❌ 容器未运行"
    exit 1
fi
echo ""

# 测试文件上传和反编译
echo "3. 测试文件上传和反编译..."
if [ -f "test-chinese-comments/target/chinese-comment-test-1.0.0.jar" ]; then
    echo "📁 使用测试文件: test-chinese-comments/target/chinese-comment-test-1.0.0.jar"
    
    # 上传文件并获取响应
    response=$(curl -s -X POST -F "jarFile=@test-chinese-comments/target/chinese-comment-test-1.0.0.jar" http://localhost:8080/upload)
    
    if echo "$response" | grep -q "Decompilation Successful"; then
        echo "✅ 文件上传和反编译成功"
        
        # 检查项目结构是否包含中文
        if echo "$response" | grep -q "ChineseCommentTest.java"; then
            echo "✅ 项目结构正确显示"
        else
            echo "⚠️  项目结构显示异常"
        fi
    else
        echo "❌ 文件上传或反编译失败"
        echo "响应内容: $response"
    fi
else
    echo "❌ 测试文件不存在"
fi
echo ""

# 检查容器日志中的处理信息
echo "4. 检查反编译处理日志..."
recent_logs=$(docker logs jar-decompiler-service --tail 10)
if echo "$recent_logs" | grep -q "ChineseCommentTest"; then
    echo "✅ 日志显示正在处理测试文件"
    echo "📋 相关日志:"
    echo "$recent_logs" | grep -E "(Processing|ChineseCommentTest)"
else
    echo "⚠️  日志中未找到测试文件处理记录"
fi
echo ""

# 测试真实的JAR文件（如果存在）
echo "5. 测试真实JAR文件（如果可用）..."
real_jar_files=(
    "*.jar"
    "test-jar/target/*.jar"
    "target/*.jar"
)

found_jar=""
for pattern in "${real_jar_files[@]}"; do
    for file in $pattern; do
        if [ -f "$file" ] && [ "$file" != "test-chinese-comments/target/chinese-comment-test-1.0.0.jar" ]; then
            found_jar="$file"
            break 2
        fi
    done
done

if [ -n "$found_jar" ]; then
    echo "📁 找到真实JAR文件: $found_jar"
    echo "🔍 文件信息:"
    ls -lh "$found_jar"
    
    # 可以选择上传测试
    echo "💡 您可以手动上传此文件到 http://localhost:8080 进行测试"
else
    echo "ℹ️  未找到其他JAR文件进行测试"
fi
echo ""

# 总结修复效果
echo "📋 Unicode转义修复总结:"
echo "========================"
echo ""
echo "✅ 已实现的修复:"
echo "1. CFR反编译器配置优化"
echo "   - unicodeoutput=false"
echo "   - hideutf=false"
echo "   - outputencoding=UTF-8"
echo "   - charsetname=UTF-8"
echo ""
echo "2. Unicode转义序列转换器"
echo "   - unescapeUnicode()方法"
echo "   - 智能识别\\uXXXX格式"
echo "   - 错误处理机制"
echo ""
echo "3. 文件编码优化"
echo "   - ZIP文件UTF-8编码"
echo "   - Java文件UTF-8读写"
echo "   - 环境变量配置"
echo ""
echo "🎯 预期效果:"
echo "- \\u7cfb\\u7edf\\u7f16\\u7801 → 系统编码"
echo "- \\u7f16\\u7801\\u7ba1\\u7406 → 编码管理"
echo "- \\u6d4b\\u8bd5\\u4e2d\\u6587 → 测试中文"
echo ""
echo "🌐 测试方法:"
echo "1. 访问: http://localhost:8080"
echo "2. 上传包含中文注释的JAR文件"
echo "3. 查看反编译结果"
echo "4. 下载ZIP文件检查源码"
echo ""
echo "🎉 修复完成！服务已准备好处理包含中文注释的JAR文件。"
