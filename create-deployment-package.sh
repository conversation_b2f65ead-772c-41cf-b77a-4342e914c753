#!/bin/bash

echo "📦 创建离线部署包"
echo "=================="
echo ""

# 创建部署包目录
package_dir="jar-decompiler-offline-deployment"
rm -rf "$package_dir"
mkdir -p "$package_dir"

echo "📁 创建部署包目录: $package_dir"
echo ""

# 复制必要文件
echo "📋 复制部署文件..."

# Docker镜像文件
if [ -f "docker-export/jar-decompiler-service-1.0.0.tar.gz" ]; then
    cp "docker-export/jar-decompiler-service-1.0.0.tar.gz" "$package_dir/"
    echo "✅ Docker镜像文件"
else
    echo "❌ Docker镜像文件不存在，请先运行: ./export-docker-image.sh"
    exit 1
fi

# 部署脚本
cp "deploy-offline.sh" "$package_dir/"
echo "✅ 离线部署脚本"

# Docker Compose文件
cp "docker-compose.yml" "$package_dir/"
echo "✅ Docker Compose配置"

# 部署指南
cp "DEPLOYMENT_GUIDE.md" "$package_dir/"
echo "✅ 部署指南文档"

# JAR文件（备用部署方式）
if [ -f "target/jar-decompiler-service-1.0.0.jar" ]; then
    mkdir -p "$package_dir/jar-deployment"
    cp "target/jar-decompiler-service-1.0.0.jar" "$package_dir/jar-deployment/"
    cp "src/main/resources/application-docker.properties" "$package_dir/jar-deployment/"
    echo "✅ JAR文件（备用部署）"
fi

# 创建快速启动脚本
cat > "$package_dir/quick-start.sh" << 'EOF'
#!/bin/bash

echo "🚀 JAR反编译服务 - 快速启动"
echo "=========================="
echo ""
echo "请选择部署方式:"
echo "1. Docker部署（推荐）"
echo "2. JAR直接部署"
echo ""
read -p "请输入选择 (1 或 2): " choice

case $choice in
    1)
        echo ""
        echo "🐳 使用Docker部署..."
        chmod +x deploy-offline.sh
        ./deploy-offline.sh
        ;;
    2)
        echo ""
        echo "☕ 使用JAR直接部署..."
        if [ -d "jar-deployment" ]; then
            cd jar-deployment
            echo "启动服务..."
            java -jar jar-decompiler-service-1.0.0.jar --spring.config.location=application-docker.properties
        else
            echo "❌ JAR部署文件不存在"
            exit 1
        fi
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac
EOF

chmod +x "$package_dir/quick-start.sh"
echo "✅ 快速启动脚本"

# 创建README文件
cat > "$package_dir/README.md" << 'EOF'
# JAR反编译服务 - 离线部署包

## 📋 包含文件

- `jar-decompiler-service-1.0.0.tar.gz` - Docker镜像文件
- `deploy-offline.sh` - 离线部署脚本
- `docker-compose.yml` - Docker Compose配置
- `quick-start.sh` - 快速启动脚本
- `DEPLOYMENT_GUIDE.md` - 详细部署指南
- `jar-deployment/` - JAR直接部署文件（备用）

## 🚀 快速部署

### 方法一：一键部署
```bash
chmod +x quick-start.sh
./quick-start.sh
```

### 方法二：Docker部署
```bash
chmod +x deploy-offline.sh
./deploy-offline.sh
```

### 方法三：JAR部署
```bash
cd jar-deployment
java -jar jar-decompiler-service-1.0.0.jar --spring.config.location=application-docker.properties
```

## 🌐 访问服务

部署成功后访问: http://服务器IP:8080

## 📖 详细说明

请查看 `DEPLOYMENT_GUIDE.md` 获取完整部署指南。
EOF

echo "✅ README文档"
echo ""

# 显示包内容
echo "📊 部署包内容:"
find "$package_dir" -type f -exec ls -lh {} \; | awk '{print $9, $5}'
echo ""

# 计算总大小
total_size=$(du -sh "$package_dir" | cut -f1)
echo "📦 部署包总大小: $total_size"
echo ""

# 创建压缩包
echo "🗜️  创建压缩包..."
tar -czf "${package_dir}.tar.gz" "$package_dir"

if [ $? -eq 0 ]; then
    echo "✅ 压缩包创建成功: ${package_dir}.tar.gz"
    echo ""
    echo "📊 最终文件:"
    ls -lh "${package_dir}.tar.gz"
    echo ""
    echo "🎉 离线部署包创建完成！"
    echo ""
    echo "📋 使用说明:"
    echo "1. 将 ${package_dir}.tar.gz 传输到目标服务器"
    echo "2. 解压: tar -xzf ${package_dir}.tar.gz"
    echo "3. 进入目录: cd $package_dir"
    echo "4. 运行: ./quick-start.sh"
    echo ""
else
    echo "❌ 压缩包创建失败"
    exit 1
fi
