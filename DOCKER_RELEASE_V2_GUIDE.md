# JAR反编译服务 v2.0.0 - Docker发布指南

**发布时间**: 2025-05-28  
**版本**: 2.0.0  
**状态**: ✅ 已完成打包，可用于离线部署

## 🎯 v2.0.0 新特性

### 主要改进
1. **文件大小支持**: 100MB → **500MB**
2. **中文注释修复**: Unicode转义序列自动转换为中文
3. **内存优化**: JVM堆内存 512MB → **2GB**
4. **容器资源**: 内存限制 1GB → **3GB**，CPU 1核 → **2核**
5. **编码增强**: 多编码格式支持，智能字符检测

### 技术改进
- **CFR配置优化**: 强制UTF-8编码，禁用Unicode转义
- **智能Unicode处理**: 自动转换 `\uXXXX` 格式为中文字符
- **多编码支持**: UTF-8, GBK, ISO-8859-1自动检测
- **系统级编码**: 完整的环境变量配置

## 📦 发布包内容

### 主要文件
```
jar-decompiler-offline-deployment-v2.tar.gz (139MB)
├── jar-decompiler-service-2.0.0.tar.gz     # Docker镜像 (115MB)
├── deploy-offline.sh                        # 离线部署脚本
├── docker-compose.yml                       # Docker Compose配置
├── quick-start.sh                          # 快速启动脚本
├── DEPLOYMENT_GUIDE.md                     # 详细部署指南
├── README.md                               # 说明文档
└── jar-deployment/                         # JAR直接部署（备用）
    ├── jar-decompiler-service-1.0.0.jar
    └── application-docker.properties
```

### 文件大小对比
| 组件 | v1.0.0 | v2.0.0 | 说明 |
|------|--------|--------|------|
| Docker镜像 | 110MB | 115MB | 增加了Unicode处理功能 |
| 部署包总大小 | 133MB | 139MB | 包含更多功能和文档 |
| 支持文件大小 | 100MB | 500MB | 5倍提升 |
| JVM内存 | 512MB | 2GB | 4倍提升 |

## 🚀 离线部署步骤

### 1. 传输文件到目标服务器
```bash
# 将部署包传输到目标服务器
scp jar-decompiler-offline-deployment-v2.tar.gz user@server:/path/to/deploy/
```

### 2. 解压部署包
```bash
# 在目标服务器上解压
tar -xzf jar-decompiler-offline-deployment-v2.tar.gz
cd jar-decompiler-offline-deployment-v2
```

### 3. 快速部署
```bash
# 方法一：一键部署
chmod +x quick-start.sh
./quick-start.sh

# 方法二：Docker部署
chmod +x deploy-offline.sh
./deploy-offline.sh

# 方法三：Docker Compose部署
docker-compose up -d
```

### 4. 验证部署
```bash
# 检查服务状态
curl http://localhost:8080/api/health
# 期望输出: JAR Decompiler Service is running

# 检查容器状态
docker ps | grep jar-decompiler-service
```

## 🔍 版本对比

### v1.0.0 vs v2.0.0
| 功能 | v1.0.0 | v2.0.0 |
|------|--------|--------|
| 最大文件大小 | 100MB | **500MB** |
| JVM内存 | 512MB | **2GB** |
| 容器内存 | 1GB | **3GB** |
| CPU核心 | 1核 | **2核** |
| Unicode处理 | 基础 | **增强** |
| 中文注释 | 部分乱码 | **完全修复** |
| 编码支持 | UTF-8 | **多编码** |
| 错误处理 | 基础 | **智能** |

### Unicode修复效果
**v1.0.0 输出**:
```java
@Api(tags={"\u7cfb\u7edf\u7f16\u7801-\u63a5\u53e3"})
```

**v2.0.0 输出**:
```java
@Api(tags={"系统编码-接口"})
```

## 🛠 技术规格

### 系统要求
- **操作系统**: Linux (推荐 Ubuntu 18.04+, CentOS 7+)
- **Docker版本**: 20.10+
- **内存**: 最少4GB (推荐8GB+)
- **磁盘空间**: 最少2GB可用空间
- **CPU**: 最少2核心

### 网络要求
- **端口**: 8080 (可配置)
- **防火墙**: 允许8080端口访问
- **代理**: 支持反向代理配置

### 性能指标
- **启动时间**: 30-60秒
- **内存使用**: 1-2GB (取决于文件大小)
- **处理能力**: 最大500MB JAR文件
- **并发用户**: 支持多用户同时使用

## 📋 部署验证清单

### 部署前检查
- [ ] 目标服务器满足系统要求
- [ ] Docker已安装并运行
- [ ] 端口8080可用
- [ ] 有足够的磁盘空间

### 部署后验证
- [ ] 容器正常启动
- [ ] 健康检查通过
- [ ] Web界面可访问
- [ ] 文件上传功能正常
- [ ] 反编译功能正常
- [ ] 中文注释正确显示
- [ ] 大文件处理正常

## 🔧 故障排除

### 常见问题
1. **容器启动失败**
   - 检查Docker服务状态
   - 检查端口占用情况
   - 查看容器日志

2. **内存不足**
   - 增加系统内存
   - 调整JVM参数
   - 减少并发处理

3. **文件上传失败**
   - 检查文件大小限制
   - 检查磁盘空间
   - 检查网络连接

4. **中文显示异常**
   - 确认使用v2.0.0版本
   - 检查环境变量设置
   - 重启容器

### 日志查看
```bash
# 查看容器日志
docker logs jar-decompiler-service

# 实时查看日志
docker logs -f jar-decompiler-service

# 查看最近日志
docker logs --tail 50 jar-decompiler-service
```

## 📞 技术支持

### 联系方式
- **维护者**: huruifeng
- **邮箱**: <EMAIL>
- **版本**: 2.0.0
- **发布日期**: 2025-05-28

### 更新说明
- 定期检查新版本发布
- 建议在测试环境先验证
- 生产环境部署前做好备份

---

**🎉 JAR反编译服务 v2.0.0 已准备就绪！支持500MB大文件和完美的中文注释显示。**
