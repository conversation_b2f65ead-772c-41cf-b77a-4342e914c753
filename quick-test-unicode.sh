#!/bin/bash

echo "🚀 快速测试Unicode转义修复"
echo "=========================="
echo ""

# 创建包含Unicode转义的测试字符串
test_strings=(
    "\\u7cfb\\u7edf\\u7f16\\u7801-\\u63a5\\u53e3\\uff08\\u4f9b\\u5176\\u4ed6\\u5e94\\u7528\\u8c03\\u7528\\uff09"
    "\\u7f16\\u7801\\u7ba1\\u7406-\\u83b7\\u53d6\\u7f16\\u7801\\u89c4\\u5219\\u53ca\\u6240\\u6709\\u7801\\u6bb5"
    "\\u6d4b\\u8bd5\\u4e2d\\u6587\\u5b57\\u7b26\\u4e32"
)

expected_results=(
    "系统编码-接口（供其他应用调用）"
    "编码管理-获取编码规则及所有码段"
    "测试中文字符串"
)

echo "📋 测试Unicode转义序列转换:"
echo ""

for i in "${!test_strings[@]}"; do
    input="${test_strings[$i]}"
    expected="${expected_results[$i]}"
    
    echo "测试 $((i+1)):"
    echo "输入: $input"
    echo "期望: $expected"
    
    # 使用printf进行转换测试
    converted=$(printf "$input")
    echo "转换: $converted"
    
    if [ "$converted" = "$expected" ]; then
        echo "✅ 转换正确"
    else
        echo "❌ 转换不匹配"
    fi
    echo ""
done

echo "🔧 修复总结:"
echo "============"
echo ""
echo "✅ 已修复的问题:"
echo "1. CFR反编译器Unicode转义问题"
echo "2. ZIP文件中文编码问题"
echo "3. 项目结构显示中文问题"
echo ""
echo "🛠️ 修复方法:"
echo "1. 添加了unescapeUnicode()方法"
echo "2. 配置CFR选项: unicodeoutput=false, hideutf=false"
echo "3. 在文件输出时自动转换Unicode转义序列"
echo "4. 增强了项目结构显示功能"
echo ""
echo "📝 修复的文件:"
echo "- src/main/java/com/jardecompiler/service/DecompilerService.java"
echo "- src/main/resources/application.properties"
echo "- src/main/resources/application-docker.properties"
echo "- Dockerfile.simple"
echo "- docker-compose.yml"
echo "- deploy-offline.sh"
echo ""
echo "🚀 下一步:"
echo "1. 重新构建项目和Docker镜像"
echo "2. 测试反编译包含中文注释的JAR文件"
echo "3. 验证下载的源码中中文字符正确显示"
echo ""
echo "💡 示例转换效果:"
echo "原始: @Api(tags={\"\\u7cfb\\u7edf\\u7f16\\u7801\"})"
echo "修复: @Api(tags={\"系统编码\"})"
