#!/bin/bash

echo "🐳 JAR反编译服务 - Docker构建脚本"
echo "=================================="
echo ""

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

# 检查Docker是否运行
if ! docker info &> /dev/null; then
    echo "❌ Docker服务未运行，请启动Docker服务"
    exit 1
fi

echo "✅ Docker环境检查通过"
echo ""

# 清理旧的镜像（可选）
echo "🧹 清理旧的Docker镜像..."
docker rmi jar-decompiler-service:1.0.0 2>/dev/null || true
echo ""

# 构建Docker镜像
echo "🔨 开始构建Docker镜像..."
echo "镜像名称: jar-decompiler-service:1.0.0"
echo ""

# 记录开始时间
start_time=$(date +%s)

# 执行构建
docker build -t jar-decompiler-service:1.0.0 .

# 检查构建结果
if [ $? -eq 0 ]; then
    end_time=$(date +%s)
    duration=$((end_time - start_time))
    echo ""
    echo "✅ Docker镜像构建成功！"
    echo "⏱️  构建耗时: ${duration}秒"
    echo ""
    
    # 显示镜像信息
    echo "📊 镜像信息:"
    docker images jar-decompiler-service:1.0.0
    echo ""
    
    echo "🚀 接下来可以使用以下命令运行容器:"
    echo "   ./docker-run.sh"
    echo "   或者"
    echo "   docker-compose up -d"
    echo ""
else
    echo ""
    echo "❌ Docker镜像构建失败！"
    echo "请检查错误信息并重试"
    exit 1
fi
