#!/bin/bash

echo "🚀 JAR反编译服务 - Git仓库设置和推送脚本"
echo "=============================================="
echo ""

# 检查是否在正确的目录
if [ ! -f "pom.xml" ]; then
    echo "❌ 错误: 请在项目根目录运行此脚本"
    exit 1
fi

echo "📁 当前目录: $(pwd)"
echo ""

# 1. 初始化Git仓库（如果还没有）
echo "🔧 1. 初始化Git仓库..."
if [ ! -d ".git" ]; then
    git init
    echo "✅ Git仓库已初始化"
else
    echo "✅ Git仓库已存在"
fi
echo ""

# 2. 创建.gitignore文件
echo "📝 2. 创建.gitignore文件..."
cat > .gitignore << 'EOF'
# Maven
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# IDE
.idea/
*.iws
*.iml
*.ipr
.vscode/
.settings/
.project
.classpath

# OS
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Temporary files
*.tmp
*.temp
*.swp
*.swo
*~

# Test files
test_*.html
result_*.html
upload_result.html
downloaded_project.zip
session_cookies.txt
cookies.txt
test_download.zip
decompiled-project.zip
decompile_result*.html

# Runtime
*.pid
EOF
echo "✅ .gitignore文件已创建"
echo ""

# 3. 配置Git用户信息（如果需要）
echo "👤 3. 配置Git用户信息..."
if [ -z "$(git config user.name)" ]; then
    echo "请输入您的Git用户名:"
    read -r git_username
    git config user.name "$git_username"
fi

if [ -z "$(git config user.email)" ]; then
    echo "请输入您的Git邮箱:"
    read -r git_email
    git config user.email "$git_email"
fi

echo "✅ Git用户: $(git config user.name) <$(git config user.email)>"
echo ""

# 4. 添加远程仓库
echo "🌐 4. 配置远程仓库..."
if git remote get-url origin >/dev/null 2>&1; then
    echo "✅ 远程仓库已配置: $(git remote get-url origin)"
else
    git remote add origin https://github.com/huruifeng03/jar2resource.git
    echo "✅ 远程仓库已添加: https://github.com/huruifeng03/jar2resource.git"
fi
echo ""

# 5. 添加文件到Git
echo "📦 5. 添加项目文件..."
git add .
echo "✅ 所有文件已添加到暂存区"
echo ""

# 6. 显示将要提交的文件
echo "📋 6. 将要提交的文件:"
git status --short
echo ""

# 7. 创建提交
echo "💾 7. 创建提交..."
commit_message="🎉 JAR反编译服务完整版

✨ 功能特性:
- 支持JAR文件上传和反编译
- 使用CFR反编译器
- 完整的项目结构展示
- ZIP文件下载功能
- 国际化支持(英语/中文/德语/法语)
- 响应式Web界面
- RESTful API接口

🔧 技术栈:
- Spring Boot 3.2.5
- Thymeleaf模板引擎
- Bootstrap 5前端框架
- CFR反编译器
- Maven构建工具

🌐 国际化:
- 支持4种语言切换
- Unicode编码修复
- 完整的本地化体验

📦 部署:
- 内嵌Tomcat服务器
- 端口8080
- 开箱即用

🧪 测试:
- 完整的功能测试脚本
- 国际化测试验证
- 上传下载流程测试"

git commit -m "$commit_message"
echo "✅ 提交已创建"
echo ""

# 8. 推送提示
echo "🚀 8. 准备推送到GitHub..."
echo ""
echo "⚠️  安全提醒:"
echo "   - 请使用Personal Access Token而不是密码"
echo "   - 在GitHub Settings > Developer settings > Personal access tokens中创建"
echo "   - 给予repo权限"
echo ""
echo "📝 推送命令:"
echo "   git push -u origin main"
echo ""
echo "🔐 如果需要认证，请使用:"
echo "   用户名: huruifeng03"
echo "   密码: [您的Personal Access Token]"
echo ""

# 9. 询问是否立即推送
echo "❓ 是否现在推送到GitHub? (y/n)"
read -r push_now

if [ "$push_now" = "y" ] || [ "$push_now" = "Y" ]; then
    echo ""
    echo "🚀 正在推送到GitHub..."
    echo "请在提示时输入您的GitHub凭据..."
    
    # 尝试推送
    if git push -u origin main; then
        echo ""
        echo "🎉 成功推送到GitHub!"
        echo "🌐 仓库地址: https://github.com/huruifeng03/jar2resource"
        echo ""
        echo "📋 下一步:"
        echo "   1. 访问GitHub仓库查看代码"
        echo "   2. 添加README.md描述"
        echo "   3. 设置仓库描述和标签"
        echo "   4. 考虑添加GitHub Actions CI/CD"
    else
        echo ""
        echo "❌ 推送失败，请检查:"
        echo "   1. 网络连接"
        echo "   2. GitHub凭据"
        echo "   3. 仓库权限"
        echo ""
        echo "💡 您可以稍后手动运行: git push -u origin main"
    fi
else
    echo ""
    echo "📝 稍后推送:"
    echo "   当您准备好时，运行: git push -u origin main"
fi

echo ""
echo "✅ Git设置完成!"
echo ""
echo "📊 项目统计:"
echo "   - 源代码文件: $(find src -name "*.java" | wc -l) 个Java文件"
echo "   - 模板文件: $(find src -name "*.html" | wc -l) 个HTML模板"
echo "   - 资源文件: $(find src -name "*.properties" | wc -l) 个配置文件"
echo "   - 文档文件: $(ls *.md | wc -l) 个Markdown文档"
echo ""
echo "🎯 仓库信息:"
echo "   - 远程地址: $(git remote get-url origin)"
echo "   - 当前分支: $(git branch --show-current)"
echo "   - 最新提交: $(git log --oneline -1)"
