version: '3.8'

services:
  jar-decompiler:
    build:
      context: .
      dockerfile: Dockerfile
    image: jar-decompiler-service:1.0.0
    container_name: jar-decompiler-service
    ports:
      - "8080:8080"
    environment:
      # Spring Boot配置
      - SPRING_PROFILES_ACTIVE=docker
      - SERVER_PORT=8080

      # 文件上传配置
      - SPRING_SERVLET_MULTIPART_MAX_FILE_SIZE=100MB
      - SPRING_SERVLET_MULTIPART_MAX_REQUEST_SIZE=100MB

      # JVM配置
      - JAVA_OPTS=-Xmx512m -Xms256m -XX:+UseG1GC -XX:+UseContainerSupport -Dfile.encoding=UTF-8 -Dsun.jnu.encoding=UTF-8

      # 字符编码设置
      - LANG=C.UTF-8
      - LC_ALL=C.UTF-8

      # 时区设置
      - TZ=Asia/Shanghai

      # 日志配置
      - LOGGING_LEVEL_COM_JARDECOMPILER=INFO
      - LOGGING_LEVEL_ORG_BENF_CFR=WARN

    volumes:
      # 挂载临时目录（可选，用于持久化临时文件）
      - jar_decompiler_temp:/app/temp

      # 挂载日志目录（可选，用于查看日志）
      - jar_decompiler_logs:/app/logs

    restart: unless-stopped

    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

    # 资源限制
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 256M
          cpus: '0.5'

    # 网络配置
    networks:
      - jar-decompiler-network

# 定义卷
volumes:
  jar_decompiler_temp:
    driver: local
  jar_decompiler_logs:
    driver: local

# 定义网络
networks:
  jar-decompiler-network:
    driver: bridge
