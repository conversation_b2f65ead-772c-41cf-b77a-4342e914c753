# 强制覆盖文件上传配置 - 支持500MB文件
spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=500MB
spring.servlet.multipart.max-request-size=500MB
spring.servlet.multipart.resolve-lazily=false
spring.servlet.multipart.file-size-threshold=10MB

# Tomcat配置
server.tomcat.max-swallow-size=500MB
server.tomcat.max-http-form-post-size=500MB
server.tomcat.connection-timeout=300000
server.tomcat.keep-alive-timeout=300000

# HTTP配置
server.max-http-request-header-size=8KB

# 连接超时配置
server.connection-timeout=300000

# 日志配置
logging.level.org.springframework.web.multipart=DEBUG
logging.level.org.apache.tomcat=DEBUG
