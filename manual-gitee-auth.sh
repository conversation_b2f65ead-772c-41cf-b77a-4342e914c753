#!/bin/bash

echo "🔐 Gitee手动认证推送脚本"
echo "========================="
echo ""

echo "🔍 当前问题: 403 Access denied"
echo "解决方案: 手动输入用户名和密码"
echo ""

# 检查当前状态
echo "📋 当前Git状态:"
echo "当前分支: $(git branch --show-current)"
echo "远程仓库: $(git remote get-url origin)"
echo ""

echo "🔧 开始修复认证问题..."
echo ""

# 步骤1: 清除所有存储的凭据
echo "1️⃣ 清除存储的凭据..."
git config --global --unset credential.helper 2>/dev/null || true
git config --unset credential.helper 2>/dev/null || true

# 删除可能存储的凭据文件
rm -f ~/.git-credentials 2>/dev/null || true

echo "✅ 凭据已清除"
echo ""

# 步骤2: 配置Git使用每次都询问密码
echo "2️⃣ 配置Git每次询问密码..."
git config --global credential.helper ""
echo "✅ 已配置为每次询问密码"
echo ""

# 步骤3: 确保远程URL正确
echo "3️⃣ 确认远程仓库URL..."
git remote set-url origin https://gitee.com/huruifeng-gitee/jar2resource.git
echo "✅ 远程URL已设置为: $(git remote get-url origin)"
echo ""

# 步骤4: 检查是否有未提交的更改
echo "4️⃣ 检查Git状态..."
if [ -n "$(git status --porcelain)" ]; then
    echo "⚠️  发现未提交的更改:"
    git status --short
    echo ""
    read -p "是否要提交这些更改? (y/n): " commit_changes
    if [ "$commit_changes" = "y" ] || [ "$commit_changes" = "Y" ]; then
        git add .
        git commit -m "feat: 更新项目文件和修复脚本"
        echo "✅ 更改已提交"
    fi
else
    echo "✅ 工作目录干净，无未提交更改"
fi
echo ""

# 步骤5: 尝试推送
echo "5️⃣ 准备推送到Gitee..."
echo ""
echo "📝 重要提示:"
echo "   - 用户名: huruifeng-gitee"
echo "   - 密码: 您的Gitee登录密码"
echo "   - 如果启用了两步验证，请使用Personal Access Token作为密码"
echo ""
echo "🔗 如需生成Token: https://gitee.com/profile/personal_access_tokens"
echo ""

read -p "准备好推送了吗? 按Enter继续..." 

echo "🚀 开始推送..."
echo "⏳ Git会提示您输入用户名和密码..."
echo ""

# 尝试推送到main分支
if git push -u origin main; then
    echo ""
    echo "🎉 推送成功!"
    echo "🌐 仓库地址: https://gitee.com/huruifeng-gitee/jar2resource"
    echo ""
    echo "📋 下一步:"
    echo "   1. 访问仓库页面确认文件已上传"
    echo "   2. 设置仓库描述和标签"
    echo "   3. 配置README显示"
    echo "   4. 考虑设置Gitee Pages"
else
    echo ""
    echo "❌ 推送失败，可能的原因:"
    echo "   1. 用户名或密码错误"
    echo "   2. 仓库不存在或无权限"
    echo "   3. 网络连接问题"
    echo "   4. 需要使用Personal Access Token"
    echo ""
    echo "🔧 建议解决方案:"
    echo ""
    echo "方案1: 使用Personal Access Token"
    echo "   1. 访问: https://gitee.com/profile/personal_access_tokens"
    echo "   2. 生成新令牌，选择 'projects' 权限"
    echo "   3. 复制令牌"
    echo "   4. 运行: git push -u origin main"
    echo "   5. 用户名输入: huruifeng-gitee"
    echo "   6. 密码输入: [您的Token]"
    echo ""
    echo "方案2: 检查仓库权限"
    echo "   1. 确认仓库存在: https://gitee.com/huruifeng-gitee/jar2resource"
    echo "   2. 确认您有推送权限"
    echo "   3. 检查分支保护设置"
    echo ""
    echo "方案3: 使用SSH连接"
    echo "   1. 生成SSH密钥: ssh-keygen -t rsa -b 4096"
    echo "   2. 添加公钥到Gitee: https://gitee.com/profile/sshkeys"
    echo "   3. 切换URL: git remote set-<NAME_EMAIL>:huruifeng-gitee/jar2resource.git"
    echo "   4. 推送: git push -u origin main"
fi

echo ""
echo "📊 项目统计:"
echo "   - Java文件: $(find src -name "*.java" 2>/dev/null | wc -l) 个"
echo "   - 模板文件: $(find src -name "*.html" 2>/dev/null | wc -l) 个"
echo "   - 配置文件: $(find src -name "*.properties" 2>/dev/null | wc -l) 个"
echo "   - 文档文件: $(ls *.md 2>/dev/null | wc -l) 个"
echo ""
echo "🎯 项目特性:"
echo "   ✅ Spring Boot 3.2.5"
echo "   ✅ 多语言支持 (英/中/德/法)"
echo "   ✅ JAR反编译功能"
echo "   ✅ 响应式Web界面"
echo "   ✅ 完整文档和测试"
