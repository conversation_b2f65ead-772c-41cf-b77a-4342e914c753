# Maven构建目录
target/
!target/jar-decompiler-service-1.0.0.jar

# IDE文件
.idea/
.vscode/
*.iml
*.ipr
*.iws

# 操作系统文件
.DS_Store
Thumbs.db

# Git文件
.git/
.gitignore

# 日志文件
*.log
logs/

# 临时文件
*.tmp
*.temp
temp/

# 测试文件
test-jar/target/
*.jar
!test-jar/target/test-jar-1.0.0.jar

# Docker文件（避免递归复制）
Dockerfile
docker-compose.yml
.dockerignore

# 脚本文件（构建时不需要）
*.sh

# 文档文件
*.md
images/

# 其他不需要的文件
cookies.txt
*.html
*.pub
huruifeng
