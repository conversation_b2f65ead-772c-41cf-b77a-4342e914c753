package com.example;

import java.util.List;
import java.util.ArrayList;

public class TestClass {
    private String name;
    private int value;
    
    public TestClass(String name, int value) {
        this.name = name;
        this.value = value;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public int getValue() {
        return value;
    }
    
    public void setValue(int value) {
        this.value = value;
    }
    
    public List<String> getItems() {
        List<String> items = new ArrayList<>();
        items.add("Item 1");
        items.add("Item 2");
        return items;
    }
    
    @Override
    public String toString() {
        return "TestClass{name='" + name + "', value=" + value + "}";
    }
    
    public static void main(String[] args) {
        TestClass test = new TestClass("Example", 42);
        System.out.println(test);
        System.out.println("Items: " + test.getItems());
    }
}
