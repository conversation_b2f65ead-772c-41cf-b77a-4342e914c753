#!/bin/bash

echo "🌍 JAR反编译服务 - 国际化和下载功能测试"
echo "============================================"
echo ""

# 检查服务状态
echo "📋 1. 检查服务状态..."
health_response=$(curl -s http://localhost:8080/api/health)
if [ "$health_response" = "JAR Decompiler Service is running" ]; then
    echo "✅ 服务运行正常"
else
    echo "❌ 服务未运行"
    exit 1
fi
echo ""

# 测试国际化功能
echo "🌐 2. 测试国际化功能..."

echo "📝 测试英语页面..."
curl -s "http://localhost:8080/?lang=en" > test_en.html
if grep -q "Upload your JAR file" test_en.html; then
    echo "✅ 英语页面正常"
else
    echo "❌ 英语页面异常"
fi

echo "📝 测试中文页面..."
curl -s "http://localhost:8080/?lang=zh" > test_zh.html
if grep -q "JAR反编译服务" test_zh.html; then
    echo "✅ 中文页面正常"
else
    echo "❌ 中文页面异常"
fi

echo "📝 测试德语页面..."
curl -s "http://localhost:8080/?lang=de" > test_de.html
if grep -q "JAR-Dekompilierungsdienst" test_de.html; then
    echo "✅ 德语页面正常"
else
    echo "❌ 德语页面异常"
fi

echo "📝 测试法语页面..."
curl -s "http://localhost:8080/?lang=fr" > test_fr.html
if grep -q "Service de Décompilation JAR" test_fr.html; then
    echo "✅ 法语页面正常"
else
    echo "❌ 法语页面异常"
fi
echo ""

# 测试完整的上传和下载流程
echo "🔄 3. 测试完整的上传和下载流程..."

echo "📤 上传JAR文件..."
curl -X POST \
  -F "jarFile=@test-jar/target/test-jar-1.0.0.jar" \
  http://localhost:8080/upload \
  -c session_cookies.txt \
  -s -o upload_result.html

if [ -f "upload_result.html" ]; then
    echo "✅ 上传成功"

    # 检查是否包含项目结构
    if grep -q "TestClass.java" upload_result.html; then
        echo "✅ 项目结构显示正常"
    else
        echo "❌ 项目结构显示异常"
    fi
else
    echo "❌ 上传失败"
    exit 1
fi

echo "💾 测试下载功能..."
curl -X POST \
  http://localhost:8080/download \
  -b session_cookies.txt \
  -s -o downloaded_project.zip

if [ -f "downloaded_project.zip" ]; then
    echo "✅ 下载成功"

    # 检查ZIP文件内容
    if unzip -l downloaded_project.zip | grep -q "TestClass.java"; then
        echo "✅ ZIP文件内容正确"
        echo "📦 ZIP文件内容:"
        unzip -l downloaded_project.zip
    else
        echo "❌ ZIP文件内容异常"
    fi
else
    echo "❌ 下载失败"
fi
echo ""

# 测试不同语言的结果页面
echo "🌐 4. 测试不同语言的结果页面..."

echo "📝 测试中文结果页面..."
curl -X POST \
  -F "jarFile=@test-jar/target/test-jar-1.0.0.jar" \
  "http://localhost:8080/upload?lang=zh" \
  -s -o result_zh.html

if grep -q "反编译成功" result_zh.html; then
    echo "✅ 中文结果页面正常"
else
    echo "❌ 中文结果页面异常"
fi

echo "📝 测试德语结果页面..."
curl -X POST \
  -F "jarFile=@test-jar/target/test-jar-1.0.0.jar" \
  "http://localhost:8080/upload?lang=de" \
  -s -o result_de.html

if grep -q "Dekompilierung erfolgreich" result_de.html; then
    echo "✅ 德语结果页面正常"
else
    echo "❌ 德语结果页面异常"
fi
echo ""

# 清理临时文件
echo "🧹 5. 清理临时文件..."
rm -f test_*.html result_*.html upload_result.html downloaded_project.zip session_cookies.txt
echo "✅ 清理完成"
echo ""

echo "🎉 测试总结:"
echo "============"
echo "✅ 服务运行正常"
echo "✅ 国际化功能正常 (英语、中文、德语、法语)"
echo "✅ JAR文件上传功能正常"
echo "✅ 项目结构显示正常"
echo "✅ ZIP文件下载功能正常"
echo "✅ 多语言结果页面正常"
echo ""
echo "🌟 所有功能测试通过！"
echo ""
echo "📖 使用说明:"
echo "- 访问 http://localhost:8080 使用Web界面"
echo "- 点击语言按钮切换界面语言"
echo "- 上传JAR文件进行反编译"
echo "- 点击下载按钮获取完整项目ZIP文件"
