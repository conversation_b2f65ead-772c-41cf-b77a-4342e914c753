#!/bin/bash

echo "🔧 测试500MB文件支持和Unicode修复"
echo "================================="
echo ""

# 检查当前配置
echo "1. 检查配置文件更新..."
echo ""

echo "📋 application.properties 文件大小配置:"
grep -E "(max-file-size|max-request-size)" src/main/resources/application.properties
echo ""

echo "📋 application-docker.properties 文件大小配置:"
grep -E "(max-file-size|max-request-size)" src/main/resources/application-docker.properties
echo ""

echo "📋 docker-compose.yml JVM配置:"
grep -A1 "JVM配置" docker-compose.yml
echo ""

echo "📋 Dockerfile.simple JVM配置:"
grep -A1 "JVM参数" Dockerfile.simple
echo ""

# 检查Unicode处理代码
echo "2. 检查Unicode处理代码..."
echo ""

echo "📋 DecompilerService.java 中的Unicode处理方法:"
if grep -q "unescapeUnicode" src/main/java/com/jardecompiler/service/DecompilerService.java; then
    echo "✅ unescapeUnicode方法已实现"
else
    echo "❌ unescapeUnicode方法未找到"
fi

if grep -q "processFileContent" src/main/java/com/jardecompiler/service/DecompilerService.java; then
    echo "✅ processFileContent方法已实现"
else
    echo "❌ processFileContent方法未找到"
fi

if grep -q "fixChineseCharacters" src/main/java/com/jardecompiler/service/DecompilerService.java; then
    echo "✅ fixChineseCharacters方法已实现"
else
    echo "❌ fixChineseCharacters方法未找到"
fi
echo ""

# 检查CFR配置
echo "3. 检查CFR反编译器配置..."
echo ""

echo "📋 CFR配置选项:"
if grep -q "outputencoding.*UTF-8" src/main/java/com/jardecompiler/service/DecompilerService.java; then
    echo "✅ outputencoding=UTF-8 已配置"
else
    echo "❌ outputencoding=UTF-8 未配置"
fi

if grep -q "unicodeoutput.*false" src/main/java/com/jardecompiler/service/DecompilerService.java; then
    echo "✅ unicodeoutput=false 已配置"
else
    echo "❌ unicodeoutput=false 未配置"
fi

if grep -q "hideutf.*false" src/main/java/com/jardecompiler/service/DecompilerService.java; then
    echo "✅ hideutf=false 已配置"
else
    echo "❌ hideutf=false 未配置"
fi
echo ""

# 创建测试用的大文件信息
echo "4. 文件大小支持测试..."
echo ""

echo "📊 当前配置支持的最大文件大小:"
echo "- 应用配置: 500MB"
echo "- Docker配置: 500MB"
echo "- JVM内存: 2GB"
echo "- 容器内存限制: 3GB"
echo ""

# 检查现有JAR文件大小
echo "📁 现有JAR文件大小检查:"
find . -name "*.jar" -type f -exec ls -lh {} \; | head -5
echo ""

# Unicode转义测试
echo "5. Unicode转义处理测试..."
echo ""

# 创建测试字符串
test_unicode_strings=(
    "\\u7cfb\\u7edf\\u7f16\\u7801-\\u63a5\\u53e3"
    "\\u7f16\\u7801\\u7ba1\\u7406-\\u83b7\\u53d6\\u7f16\\u7801"
    "\\u6d4b\\u8bd5\\u4e2d\\u6587\\u5b57\\u7b26\\u4e32"
)

expected_results=(
    "系统编码-接口"
    "编码管理-获取编码"
    "测试中文字符串"
)

echo "📋 Unicode转义序列测试:"
for i in "${!test_unicode_strings[@]}"; do
    input="${test_unicode_strings[$i]}"
    expected="${expected_results[$i]}"
    echo "输入: $input"
    echo "期望: $expected"
    echo ""
done

# 总结修复内容
echo "📋 修复总结:"
echo "============"
echo ""
echo "✅ 文件大小支持:"
echo "1. 最大文件大小: 100MB → 500MB"
echo "2. JVM内存: 512MB → 2GB"
echo "3. 容器内存: 1GB → 3GB"
echo ""
echo "✅ Unicode处理增强:"
echo "1. 增强的unescapeUnicode()方法"
echo "2. 多编码格式支持(UTF-8, GBK, ISO-8859-1)"
echo "3. 智能字符编码检测和修复"
echo "4. CFR配置优化"
echo ""
echo "✅ 系统级编码设置:"
echo "1. System.setProperty(\"file.encoding\", \"UTF-8\")"
echo "2. System.setProperty(\"sun.jnu.encoding\", \"UTF-8\")"
echo "3. 环境变量: LANG=C.UTF-8, LC_ALL=C.UTF-8"
echo ""
echo "🚀 下一步测试:"
echo "1. 重新构建Docker镜像"
echo "2. 启动服务"
echo "3. 上传大型JAR文件(最大500MB)"
echo "4. 验证中文注释正确显示"
echo ""
echo "💡 测试命令:"
echo "docker build -f Dockerfile.simple -t jar-decompiler-service:1.0.0 ."
echo "docker run -d --name jar-decompiler-service -p 8080:8080 jar-decompiler-service:1.0.0"
echo "curl http://localhost:8080/api/health"
