#!/bin/bash

echo "🔧 Gitee推送问题修复脚本"
echo "========================"
echo ""

echo "🔍 检测到的问题: 403 Access denied"
echo "这通常是认证或权限问题导致的。"
echo ""

# 检查当前Git配置
echo "📋 当前Git配置:"
echo "用户名: $(git config user.name)"
echo "邮箱: $(git config user.email)"
echo "远程仓库: $(git remote get-url origin 2>/dev/null || echo '未配置')"
echo ""

echo "🛠️ 修复方案:"
echo ""

echo "❓ 请选择修复方案:"
echo "1) 重新配置Gitee认证信息"
echo "2) 使用Personal Access Token"
echo "3) 切换到SSH连接"
echo "4) 检查仓库权限"
echo "5) 强制推送到main分支"
echo "6) 显示详细诊断信息"
echo "7) 退出"
echo ""

read -p "请输入选项 (1-7): " choice

case $choice in
    1)
        echo ""
        echo "🔧 重新配置Gitee认证..."
        
        # 清除现有凭据
        git config --global --unset credential.helper
        git config --global credential.helper store
        
        # 重新设置远程URL
        git remote set-url origin https://gitee.com/huruifeng-gitee/jar2resource.git
        
        echo "✅ 远程URL已更新"
        echo "📝 下次推送时会提示输入用户名和密码"
        echo ""
        echo "🚀 现在尝试推送:"
        git push -u origin master
        ;;
    2)
        echo ""
        echo "🔧 配置Personal Access Token..."
        echo ""
        echo "📝 请按以下步骤操作:"
        echo "1. 访问 https://gitee.com/profile/personal_access_tokens"
        echo "2. 点击 '生成新令牌'"
        echo "3. 选择权限: projects (完整权限)"
        echo "4. 复制生成的令牌"
        echo ""
        read -p "请输入您的Personal Access Token: " token
        
        if [ ! -z "$token" ]; then
            # 使用token设置远程URL
            git remote set-url origin https://huruifeng-gitee:$<EMAIL>/huruifeng-gitee/jar2resource.git
            echo "✅ Personal Access Token已配置"
            echo ""
            echo "🚀 现在尝试推送:"
            git push -u origin master
        else
            echo "❌ 未输入令牌"
        fi
        ;;
    3)
        echo ""
        echo "🔧 配置SSH连接..."
        
        # 检查SSH密钥
        if [ ! -f ~/.ssh/id_rsa.pub ]; then
            echo "📝 生成SSH密钥..."
            read -p "请输入您的邮箱: " email
            ssh-keygen -t rsa -b 4096 -C "$email" -f ~/.ssh/id_rsa -N ""
        fi
        
        echo ""
        echo "📋 您的SSH公钥:"
        echo "=================="
        cat ~/.ssh/id_rsa.pub
        echo "=================="
        echo ""
        echo "📝 请按以下步骤操作:"
        echo "1. 复制上面的SSH公钥"
        echo "2. 访问 https://gitee.com/profile/sshkeys"
        echo "3. 点击 '添加公钥'"
        echo "4. 粘贴公钥并保存"
        echo "5. 完成后按任意键继续..."
        read -n 1
        
        echo ""
        echo "🔧 切换到SSH连接..."
        git remote set-<NAME_EMAIL>:huruifeng-gitee/jar2resource.git
        echo "✅ 远程URL已切换到SSH"
        
        echo ""
        echo "🧪 测试SSH连接..."
        ssh -T *************
        
        echo ""
        echo "🚀 现在尝试推送:"
        git push -u origin master
        ;;
    4)
        echo ""
        echo "🔍 检查仓库权限..."
        echo ""
        echo "📝 请确认以下事项:"
        echo "1. 您是仓库的所有者或有推送权限"
        echo "2. 仓库存在且可访问: https://gitee.com/huruifeng-gitee/jar2resource"
        echo "3. 没有启用分支保护规则"
        echo ""
        echo "🌐 访问仓库设置:"
        echo "https://gitee.com/huruifeng-gitee/jar2resource/settings"
        echo ""
        read -p "确认权限无误后按任意键继续..." -n 1
        echo ""
        echo "🚀 尝试推送:"
        git push -u origin master
        ;;
    5)
        echo ""
        echo "🔧 尝试推送到main分支..."
        
        # 检查当前分支
        current_branch=$(git branch --show-current)
        echo "当前分支: $current_branch"
        
        # 如果不是main分支，创建并切换
        if [ "$current_branch" != "main" ]; then
            git checkout -b main 2>/dev/null || git checkout main
        fi
        
        echo "🚀 推送到main分支:"
        git push -u origin main
        
        echo ""
        echo "💡 如果成功，可能需要在Gitee设置默认分支为main"
        ;;
    6)
        echo ""
        echo "🔍 系统诊断信息:"
        echo "================"
        echo "Git版本:"
        git --version
        echo ""
        echo "当前目录:"
        pwd
        echo ""
        echo "Git状态:"
        git status --short
        echo ""
        echo "远程仓库:"
        git remote -v
        echo ""
        echo "分支信息:"
        git branch -a
        echo ""
        echo "最近提交:"
        git log --oneline -3 2>/dev/null || echo "无提交记录"
        echo ""
        echo "Git配置:"
        git config --list | grep -E "(user|remote|credential)"
        echo ""
        echo "网络连接测试:"
        curl -I https://gitee.com 2>/dev/null | head -1 || echo "❌ 无法连接到Gitee"
        ;;
    7)
        echo "退出修复脚本"
        exit 0
        ;;
    *)
        echo "❌ 无效选项"
        exit 1
        ;;
esac

echo ""
echo "🎯 其他建议:"
echo "- 确保Gitee用户名和仓库名正确"
echo "- 检查是否需要验证邮箱"
echo "- 尝试在Gitee网页端创建仓库"
echo "- 联系Gitee客服如果问题持续"
echo ""
echo "📚 更多帮助:"
echo "- Gitee SSH配置: https://gitee.com/help/articles/4181"
echo "- Personal Access Token: https://gitee.com/help/articles/4253"
