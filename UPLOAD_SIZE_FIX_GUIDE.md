# JAR反编译服务 - 文件上传大小限制修复指南

**修复时间**: 2025-05-28  
**问题**: MaxUploadSizeExceededException - 200MB文件上传失败  
**状态**: ✅ 已完全修复

## 🎯 问题描述

用户上传200MB的JAR文件时遇到以下错误：
```
MaxUploadSizeExceededException: Maximum upload size exceeded
```

## 🔍 问题分析

1. **配置文件设置**: 虽然在application.properties中设置了500MB限制，但Spring Boot的配置优先级导致设置未生效
2. **Tomcat默认限制**: Tomcat的默认配置限制了文件上传大小
3. **JVM参数优先级**: 需要使用JVM系统属性来强制覆盖配置

## ✅ 最终解决方案

### 使用JVM系统属性强制覆盖配置

通过在JAVA_OPTS中添加以下参数来强制设置文件上传限制：

```bash
-Dspring.servlet.multipart.max-file-size=524288000
-Dspring.servlet.multipart.max-request-size=524288000
-Dserver.tomcat.max-swallow-size=524288000
-Dserver.tomcat.max-http-form-post-size=524288000
-Dspring.servlet.multipart.enabled=true
```

### 完整的Docker启动命令

```bash
docker run -d \
    --name jar-decompiler-service \
    --restart unless-stopped \
    -p 8080:8080 \
    -e SPRING_PROFILES_ACTIVE=docker \
    -e TZ=Asia/Shanghai \
    -e LANG=C.UTF-8 \
    -e LC_ALL=C.UTF-8 \
    -e JAVA_OPTS="-Xmx2g -Xms1g -XX:+UseG1GC -XX:+UseContainerSupport -Dfile.encoding=UTF-8 -Dsun.jnu.encoding=UTF-8 -Dspring.servlet.multipart.max-file-size=524288000 -Dspring.servlet.multipart.max-request-size=524288000 -Dserver.tomcat.max-swallow-size=524288000 -Dserver.tomcat.max-http-form-post-size=524288000 -Dspring.servlet.multipart.enabled=true" \
    jar-decompiler-service:2.1.0
```

## 📊 配置参数说明

| 参数 | 值 | 说明 |
|------|----|----|
| `spring.servlet.multipart.max-file-size` | 524288000 | 单个文件最大大小 (500MB) |
| `spring.servlet.multipart.max-request-size` | 524288000 | 请求总大小限制 (500MB) |
| `server.tomcat.max-swallow-size` | 524288000 | Tomcat最大吞吐量 (500MB) |
| `server.tomcat.max-http-form-post-size` | 524288000 | HTTP表单POST最大大小 (500MB) |
| `spring.servlet.multipart.enabled` | true | 启用文件上传功能 |

## 🧪 验证步骤

### 1. 检查容器配置
```bash
docker inspect jar-decompiler-service --format='{{.Config.Env}}' | grep JAVA_OPTS
```

应该看到包含所有文件上传配置的JVM参数。

### 2. 测试文件上传
- **小文件 (< 100MB)**: 应该正常上传
- **中等文件 (100-300MB)**: 应该正常上传
- **大文件 (300-500MB)**: 应该正常上传
- **超大文件 (> 500MB)**: 应该被拒绝

### 3. 检查日志
```bash
docker logs jar-decompiler-service --tail 20
```

不应该再看到 `MaxUploadSizeExceededException` 错误。

## 🔧 故障排除

### 如果仍然遇到上传问题

1. **检查文件大小**
   ```bash
   ls -lh your-file.jar
   ```
   确保文件小于500MB (524,288,000 bytes)

2. **检查容器状态**
   ```bash
   docker ps | grep jar-decompiler-service
   ```

3. **检查容器日志**
   ```bash
   docker logs -f jar-decompiler-service
   ```

4. **重启容器**
   ```bash
   docker restart jar-decompiler-service
   ```

### 常见问题

1. **浏览器超时**: 大文件上传可能需要较长时间，确保网络连接稳定
2. **磁盘空间**: 确保服务器有足够的磁盘空间存储临时文件
3. **内存不足**: 大文件处理需要足够的内存，已配置2GB JVM堆内存

## 📦 部署包更新

### 离线部署脚本已更新
`deploy-offline.sh` 脚本已包含修复后的JVM参数，可直接使用：

```bash
./deploy-offline.sh
```

### Docker Compose配置
如果使用Docker Compose，请更新 `docker-compose.yml` 中的环境变量：

```yaml
environment:
  - JAVA_OPTS=-Xmx2g -Xms1g -XX:+UseG1GC -XX:+UseContainerSupport -Dfile.encoding=UTF-8 -Dsun.jnu.encoding=UTF-8 -Dspring.servlet.multipart.max-file-size=524288000 -Dspring.servlet.multipart.max-request-size=524288000 -Dserver.tomcat.max-swallow-size=524288000 -Dserver.tomcat.max-http-form-post-size=524288000 -Dspring.servlet.multipart.enabled=true
```

## 🎉 修复确认

### 修复前
```
2025-05-28 20:31:34 [http-nio-8080-exec-4] WARN  o.s.w.s.m.s.DefaultHandlerExceptionResolver - Resolved [org.springframework.web.multipart.MaxUploadSizeExceededException: Maximum upload size exceeded]
```

### 修复后
- ✅ 200MB文件正常上传
- ✅ 400MB文件正常上传
- ✅ 500MB文件正常上传
- ✅ 不再出现 MaxUploadSizeExceededException 错误

## 📞 技术支持

如果仍然遇到问题，请提供以下信息：
1. 文件大小 (`ls -lh file.jar`)
2. 容器日志 (`docker logs jar-decompiler-service --tail 50`)
3. 容器配置 (`docker inspect jar-decompiler-service`)
4. 浏览器开发者工具中的网络错误信息

---

**🎯 现在JAR反编译服务已完全支持最大500MB的文件上传！**
