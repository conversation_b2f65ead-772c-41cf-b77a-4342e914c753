#!/bin/bash

echo "🐳 JAR反编译服务 - Docker运行脚本"
echo "=================================="
echo ""

# 检查Docker镜像是否存在
if ! docker images jar-decompiler-service:1.0.0 | grep -q jar-decompiler-service; then
    echo "❌ Docker镜像不存在，请先构建镜像:"
    echo "   ./docker-build.sh"
    exit 1
fi

# 停止并删除已存在的容器
echo "🧹 清理已存在的容器..."
docker stop jar-decompiler-service 2>/dev/null || true
docker rm jar-decompiler-service 2>/dev/null || true
echo ""

# 运行Docker容器
echo "🚀 启动Docker容器..."
echo "容器名称: jar-decompiler-service"
echo "端口映射: 8080:8080"
echo "访问地址: http://localhost:8080"
echo ""

docker run -d \
    --name jar-decompiler-service \
    --restart unless-stopped \
    -p 8080:8080 \
    -e SPRING_PROFILES_ACTIVE=docker \
    -e TZ=Asia/Shanghai \
    -v jar_decompiler_temp:/app/temp \
    -v jar_decompiler_logs:/app/logs \
    jar-decompiler-service:1.0.0

# 检查容器是否启动成功
if [ $? -eq 0 ]; then
    echo "✅ 容器启动成功！"
    echo ""
    
    # 等待服务启动
    echo "⏳ 等待服务启动..."
    sleep 10
    
    # 检查服务健康状态
    echo "🔍 检查服务状态..."
    for i in {1..30}; do
        if curl -s http://localhost:8080/api/health > /dev/null 2>&1; then
            echo "✅ 服务启动成功！"
            echo ""
            echo "📊 容器状态:"
            docker ps | grep jar-decompiler-service
            echo ""
            echo "🌐 访问地址:"
            echo "   Web界面: http://localhost:8080"
            echo "   健康检查: http://localhost:8080/api/health"
            echo ""
            echo "📝 常用命令:"
            echo "   查看日志: docker logs jar-decompiler-service"
            echo "   停止容器: docker stop jar-decompiler-service"
            echo "   重启容器: docker restart jar-decompiler-service"
            echo "   删除容器: docker rm jar-decompiler-service"
            echo ""
            break
        else
            echo "⏳ 等待服务启动... ($i/30)"
            sleep 2
        fi
    done
    
    if [ $i -eq 30 ]; then
        echo "⚠️  服务启动超时，请检查日志:"
        echo "   docker logs jar-decompiler-service"
    fi
else
    echo "❌ 容器启动失败！"
    exit 1
fi
