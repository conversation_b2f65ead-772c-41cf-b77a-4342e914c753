#!/bin/bash

echo "🚀 JAR Decompiler Service - 完整演示"
echo "=================================="
echo ""

# 检查服务状态
echo "📋 1. 检查服务状态..."
health_response=$(curl -s http://localhost:8080/api/health)
if [ "$health_response" = "JAR Decompiler Service is running" ]; then
    echo "✅ 服务运行正常"
else
    echo "❌ 服务未运行，请先启动服务: mvn spring-boot:run"
    exit 1
fi
echo ""

# 显示测试JAR文件信息
echo "📦 2. 测试JAR文件信息..."
if [ -f "test-jar/target/test-jar-1.0.0.jar" ]; then
    echo "✅ 测试JAR文件: test-jar/target/test-jar-1.0.0.jar"
    echo "📏 文件大小: $(du -h test-jar/target/test-jar-1.0.0.jar | cut -f1)"
    echo "🔍 文件内容:"
    jar -tf test-jar/target/test-jar-1.0.0.jar | head -10
else
    echo "❌ 测试JAR文件不存在，请先构建: cd test-jar && mvn clean package"
    exit 1
fi
echo ""

# 测试反编译功能
echo "🔧 3. 测试反编译功能..."
echo "正在上传并反编译JAR文件..."
curl -X POST \
  -F "jarFile=@test-jar/target/test-jar-1.0.0.jar" \
  http://localhost:8080/upload \
  -s -o demo_result.html

if [ -f "demo_result.html" ]; then
    echo "✅ 反编译成功完成"
    echo "📄 结果文件大小: $(du -h demo_result.html | cut -f1)"
    
    # 提取项目结构信息
    if grep -q "TestClass.java" demo_result.html; then
        echo "✅ 检测到反编译的Java文件: TestClass.java"
    fi
else
    echo "❌ 反编译失败"
    exit 1
fi
echo ""

# 测试下载功能
echo "💾 4. 测试下载功能..."
echo "正在下载反编译项目ZIP文件..."
curl -X POST \
  -F "jarFile=@test-jar/target/test-jar-1.0.0.jar" \
  http://localhost:8080/download \
  -s -o demo_project.zip

if [ -f "demo_project.zip" ]; then
    echo "✅ 下载成功完成"
    echo "📦 ZIP文件大小: $(du -h demo_project.zip | cut -f1)"
    echo "📁 ZIP文件内容:"
    unzip -l demo_project.zip
    echo ""
    
    # 解压并查看反编译的代码
    echo "📝 反编译的Java代码预览:"
    unzip -q demo_project.zip
    if [ -f "com/example/TestClass.java" ]; then
        echo "--- TestClass.java (前20行) ---"
        head -20 com/example/TestClass.java
        echo "..."
        echo "--- 文件结束 ---"
    fi
else
    echo "❌ 下载失败"
    exit 1
fi
echo ""

# 清理临时文件
echo "🧹 5. 清理临时文件..."
rm -f demo_result.html demo_project.zip
rm -rf com summary.txt
echo "✅ 清理完成"
echo ""

# 显示使用说明
echo "🎯 使用说明:"
echo "============"
echo "1. 🌐 Web界面: http://localhost:8080"
echo "2. 📤 上传JAR文件进行反编译"
echo "3. 👀 查看反编译的项目结构"
echo "4. 💾 下载完整的源代码项目"
echo ""
echo "📚 API接口:"
echo "- GET  /api/health          - 健康检查"
echo "- POST /upload              - 上传并反编译"
echo "- POST /download            - 下载反编译项目"
echo ""
echo "🎉 演示完成！JAR反编译服务运行正常。"
