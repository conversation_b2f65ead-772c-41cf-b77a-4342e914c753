#!/bin/bash

echo "🔧 Git SSL连接问题修复脚本"
echo "=========================="
echo ""

echo "🔍 检测到的问题: SSL_ERROR_SYSCALL"
echo "这通常是由于网络、代理或SSL配置问题导致的。"
echo ""

echo "🛠️ 尝试以下解决方案:"
echo ""

# 方案1: 配置Git忽略SSL证书验证（临时解决）
echo "📝 方案1: 临时禁用SSL验证 (不推荐用于生产环境)"
echo "git config --global http.sslVerify false"
echo ""

# 方案2: 更新Git配置
echo "📝 方案2: 更新Git SSL配置"
echo "git config --global http.sslBackend openssl"
echo "git config --global http.sslCAInfo /etc/ssl/certs/ca-certificates.crt"
echo ""

# 方案3: 使用SSH代替HTTPS
echo "📝 方案3: 使用SSH连接 (推荐)"
echo "git remote set-<NAME_EMAIL>:huruifeng03/jar2resource.git"
echo ""

# 方案4: 配置代理（如果在企业网络环境）
echo "📝 方案4: 配置代理 (如果需要)"
echo "git config --global http.proxy http://proxy.company.com:port"
echo "git config --global https.proxy https://proxy.company.com:port"
echo ""

echo "❓ 请选择要执行的修复方案:"
echo "1) 临时禁用SSL验证 (快速解决)"
echo "2) 更新SSL配置"
echo "3) 切换到SSH连接 (推荐)"
echo "4) 手动配置代理"
echo "5) 显示详细诊断信息"
echo "6) 退出"
echo ""

read -p "请输入选项 (1-6): " choice

case $choice in
    1)
        echo ""
        echo "🔧 正在禁用SSL验证..."
        git config --global http.sslVerify false
        echo "✅ SSL验证已禁用"
        echo "⚠️  警告: 这会降低安全性，仅用于临时解决问题"
        echo ""
        echo "现在尝试推送:"
        git push -u origin main
        ;;
    2)
        echo ""
        echo "🔧 正在更新SSL配置..."
        git config --global http.sslBackend openssl
        git config --global http.version HTTP/1.1
        echo "✅ SSL配置已更新"
        echo ""
        echo "现在尝试推送:"
        git push -u origin main
        ;;
    3)
        echo ""
        echo "🔧 正在切换到SSH连接..."
        
        # 检查是否已有SSH密钥
        if [ ! -f ~/.ssh/id_rsa.pub ]; then
            echo "📝 未找到SSH密钥，正在生成..."
            echo "请按Enter使用默认设置，或输入您的邮箱:"
            read -p "邮箱 (可选): " email
            if [ -z "$email" ]; then
                ssh-keygen -t rsa -b 4096 -f ~/.ssh/id_rsa -N ""
            else
                ssh-keygen -t rsa -b 4096 -C "$email" -f ~/.ssh/id_rsa -N ""
            fi
        fi
        
        echo ""
        echo "📋 您的SSH公钥:"
        echo "=================="
        cat ~/.ssh/id_rsa.pub
        echo "=================="
        echo ""
        echo "📝 请按以下步骤操作:"
        echo "1. 复制上面的SSH公钥"
        echo "2. 访问 https://github.com/settings/keys"
        echo "3. 点击 'New SSH key'"
        echo "4. 粘贴公钥并保存"
        echo "5. 完成后按任意键继续..."
        read -n 1
        
        echo ""
        echo "🔧 正在切换远程URL到SSH..."
        git remote set-<NAME_EMAIL>:huruifeng03/jar2resource.git
        echo "✅ 远程URL已切换到SSH"
        
        echo ""
        echo "🧪 测试SSH连接..."
        ssh -T **************
        
        echo ""
        echo "现在尝试推送:"
        git push -u origin main
        ;;
    4)
        echo ""
        echo "🔧 配置代理设置..."
        read -p "请输入HTTP代理地址 (格式: http://proxy.com:port): " http_proxy
        read -p "请输入HTTPS代理地址 (格式: https://proxy.com:port): " https_proxy
        
        if [ ! -z "$http_proxy" ]; then
            git config --global http.proxy "$http_proxy"
            echo "✅ HTTP代理已设置: $http_proxy"
        fi
        
        if [ ! -z "$https_proxy" ]; then
            git config --global https.proxy "$https_proxy"
            echo "✅ HTTPS代理已设置: $https_proxy"
        fi
        
        echo ""
        echo "现在尝试推送:"
        git push -u origin main
        ;;
    5)
        echo ""
        echo "🔍 系统诊断信息:"
        echo "================"
        echo "Git版本:"
        git --version
        echo ""
        echo "Git配置:"
        git config --list | grep -E "(http|ssl|proxy)"
        echo ""
        echo "网络连接测试:"
        echo "测试GitHub连接..."
        curl -I https://github.com 2>/dev/null | head -1 || echo "❌ 无法连接到GitHub"
        echo ""
        echo "SSL证书信息:"
        echo | openssl s_client -connect github.com:443 2>/dev/null | openssl x509 -noout -dates 2>/dev/null || echo "❌ SSL连接失败"
        echo ""
        echo "DNS解析:"
        nslookup github.com || echo "❌ DNS解析失败"
        ;;
    6)
        echo "退出修复脚本"
        exit 0
        ;;
    *)
        echo "❌ 无效选项"
        exit 1
        ;;
esac

echo ""
echo "🎯 其他建议:"
echo "- 如果在企业网络环境，请联系IT部门获取代理设置"
echo "- 确保防火墙允许Git连接"
echo "- 尝试使用不同的网络环境"
echo "- 更新Git到最新版本"
echo ""
echo "📚 更多帮助:"
echo "- GitHub SSH设置: https://docs.github.com/en/authentication/connecting-to-github-with-ssh"
echo "- Git代理配置: https://git-scm.com/docs/git-config#Documentation/git-config.txt-httpproxy"
