package com.test;

/**
 * 中文注释测试类
 * 这是一个用于测试反编译器处理中文注释的示例类
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class ChineseCommentTest {
    
    /**
     * 私有字段 - 用户名称
     */
    private String userName;
    
    /**
     * 私有字段 - 用户年龄
     */
    private int userAge;
    
    /**
     * 构造函数
     * @param userName 用户名称
     * @param userAge 用户年龄
     */
    public ChineseCommentTest(String userName, int userAge) {
        this.userName = userName;
        this.userAge = userAge;
    }
    
    /**
     * 获取用户信息
     * @return 返回格式化的用户信息字符串
     */
    public String getUserInfo() {
        // 这是一个行内注释，包含中文字符
        return "用户名：" + userName + "，年龄：" + userAge + "岁";
    }
    
    /**
     * 设置用户名称
     * @param userName 新的用户名称
     */
    public void setUserName(String userName) {
        // 验证用户名不能为空
        if (userName != null && !userName.trim().isEmpty()) {
            this.userName = userName;
        } else {
            throw new IllegalArgumentException("用户名不能为空");
        }
    }
    
    /**
     * 获取用户名称
     * @return 用户名称
     */
    public String getUserName() {
        return userName;
    }
    
    /**
     * 设置用户年龄
     * @param userAge 新的用户年龄
     */
    public void setUserAge(int userAge) {
        // 验证年龄范围
        if (userAge >= 0 && userAge <= 150) {
            this.userAge = userAge;
        } else {
            throw new IllegalArgumentException("年龄必须在0-150之间");
        }
    }
    
    /**
     * 获取用户年龄
     * @return 用户年龄
     */
    public int getUserAge() {
        return userAge;
    }
    
    /**
     * 主方法 - 程序入口点
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        // 创建测试实例
        ChineseCommentTest test = new ChineseCommentTest("张三", 25);
        
        // 输出用户信息
        System.out.println(test.getUserInfo());
        
        // 修改用户信息
        test.setUserName("李四");
        test.setUserAge(30);
        
        // 再次输出用户信息
        System.out.println("修改后的信息：" + test.getUserInfo());
    }
}
