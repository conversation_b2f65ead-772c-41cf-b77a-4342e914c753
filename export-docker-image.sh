#!/bin/bash

echo "📦 JAR反编译服务 - Docker镜像导出脚本"
echo "====================================="
echo ""

# 检查Docker是否运行
if ! docker info &> /dev/null; then
    echo "❌ Docker服务未运行，请启动Docker服务"
    exit 1
fi

# 检查镜像是否存在
if ! docker images jar-decompiler-service:2.0.0 | grep -q jar-decompiler-service; then
    echo "❌ Docker镜像不存在，请先构建镜像"
    echo "   运行: ./build-and-run.sh"
    exit 1
fi

echo "✅ 找到Docker镜像: jar-decompiler-service:2.0.0"
echo ""

# 显示镜像信息
echo "📊 镜像信息:"
docker images jar-decompiler-service:2.0.0
echo ""

# 创建导出目录
export_dir="docker-export"
mkdir -p "$export_dir"

echo "🚀 开始导出Docker镜像..."
echo "导出目录: $export_dir/"
echo ""

# 导出镜像
image_file="$export_dir/jar-decompiler-service-2.0.0.tar"
echo "📤 导出镜像到文件: $image_file"
docker save -o "$image_file" jar-decompiler-service:2.0.0

if [ $? -eq 0 ]; then
    echo "✅ 镜像导出成功！"
else
    echo "❌ 镜像导出失败！"
    exit 1
fi

# 显示文件信息
echo ""
echo "📊 导出文件信息:"
ls -lh "$image_file"
echo ""

# 压缩镜像文件（可选）
echo "🗜️  压缩镜像文件..."
gzip "$image_file"
compressed_file="${image_file}.gz"

if [ -f "$compressed_file" ]; then
    echo "✅ 压缩完成！"
    echo ""
    echo "📊 压缩后文件信息:"
    ls -lh "$compressed_file"
    echo ""

    # 计算压缩比
    original_size=$(docker images jar-decompiler-service:2.0.0 --format "table {{.Size}}" | tail -n 1)
    compressed_size=$(ls -lh "$compressed_file" | awk '{print $5}')
    echo "📈 压缩效果:"
    echo "   原始镜像大小: $original_size"
    echo "   压缩文件大小: $compressed_size"
else
    echo "⚠️  压缩失败，使用未压缩文件"
    compressed_file="$image_file"
fi

echo ""
echo "🎉 导出完成！"
echo ""
echo "📁 导出文件位置:"
echo "   $compressed_file"
echo ""
echo "📋 离线部署步骤:"
echo "1. 将以下文件复制到目标服务器:"
echo "   - $compressed_file"
echo "   - docker-compose.yml"
echo "   - deploy-offline.sh"
echo ""
echo "2. 在目标服务器上运行:"
echo "   chmod +x deploy-offline.sh"
echo "   ./deploy-offline.sh"
echo ""
echo "3. 访问服务:"
echo "   http://服务器IP:8080"
