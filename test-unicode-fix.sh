#!/bin/bash

echo "🔧 测试Unicode转义修复效果"
echo "=========================="
echo ""

# 创建测试用的Java文件，包含Unicode转义序列
test_file="test-unicode-escape.java"
cat > "$test_file" << 'EOF'
package com.test;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags={"\u7cfb\u7edf\u7f16\u7801-\u63a5\u53e3\uff08\u4f9b\u5176\u4ed6\u5e94\u7528\u8c03\u7528\uff09"})
public class UnicodeTest {
    
    @ApiOperation(value="\u7f16\u7801\u7ba1\u7406-\u83b7\u53d6\u7f16\u7801\u89c4\u5219\u53ca\u6240\u6709\u7801\u6bb5")
    public void testMethod() {
        String message = "\u6d4b\u8bd5\u4e2d\u6587\u5b57\u7b26\u4e32";
        System.out.println(message);
    }
}
EOF

echo "📝 创建测试文件: $test_file"
echo "原始内容（包含Unicode转义）:"
echo "----------------------------------------"
cat "$test_file"
echo "----------------------------------------"
echo ""

# 测试Unicode转义转换
echo "🔄 测试Unicode转义转换..."

# 使用Java测试转换
java_test_code='
public class UnicodeConverter {
    public static void main(String[] args) {
        String input = args[0];
        StringBuilder result = new StringBuilder();
        int length = input.length();
        
        for (int i = 0; i < length; i++) {
            char ch = input.charAt(i);
            
            if (ch == '\'' && i + 1 < length && input.charAt(i + 1) == '\''u'\'') {
                if (i + 5 < length) {
                    try {
                        String hexCode = input.substring(i + 2, i + 6);
                        if (hexCode.matches("[0-9a-fA-F]{4}")) {
                            int codePoint = Integer.parseInt(hexCode, 16);
                            result.append((char) codePoint);
                            i += 5;
                        } else {
                            result.append(ch);
                        }
                    } catch (NumberFormatException e) {
                        result.append(ch);
                    }
                } else {
                    result.append(ch);
                }
            } else {
                result.append(ch);
            }
        }
        
        System.out.println(result.toString());
    }
}
'

# 创建简单的转换测试
echo "🧪 测试Unicode转义序列转换:"
echo "\\u7cfb\\u7edf\\u7f16\\u7801 应该转换为: 系统编码"
echo "\\u6d4b\\u8bd5\\u4e2d\\u6587 应该转换为: 测试中文"
echo ""

# 使用sed进行简单的转换测试
echo "📋 转换后的内容预览:"
echo "----------------------------------------"
# 这里只是示例，实际转换在Java代码中进行
sed 's/\\u7cfb\\u7edf\\u7f16\\u7801/系统编码/g; s/\\u6d4b\\u8bd5\\u4e2d\\u6587/测试中文/g' "$test_file"
echo "----------------------------------------"
echo ""

# 清理测试文件
rm -f "$test_file"

echo "✅ Unicode转义修复功能已添加到DecompilerService中"
echo ""
echo "🔧 修复内容包括:"
echo "1. ✅ 添加了unescapeUnicode()方法"
echo "2. ✅ 在ZIP文件创建时自动转换Unicode转义"
echo "3. ✅ 在项目结构显示时转换Unicode转义"
echo "4. ✅ 添加了中文字符检测功能"
echo ""
echo "📋 主要改进:"
echo "- CFR选项: unicodeoutput=false, hideutf=false"
echo "- 自动转换: \\uXXXX → 实际中文字符"
echo "- 智能检测: 只转换有效的Unicode转义序列"
echo "- 错误处理: 转换失败时保持原样"
echo ""
echo "🚀 要测试完整效果，请:"
echo "1. 重新构建项目: mvn clean package -DskipTests"
echo "2. 重新构建Docker镜像"
echo "3. 上传包含中文注释的JAR文件进行测试"
echo "4. 检查下载的源码中中文字符是否正确显示"
