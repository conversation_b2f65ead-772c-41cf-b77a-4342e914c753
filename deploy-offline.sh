#!/bin/bash

echo "🚀 JAR反编译服务 - 离线部署脚本"
echo "==============================="
echo ""

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    echo ""
    echo "📋 Docker安装指南:"
    echo "Ubuntu/Debian:"
    echo "  curl -fsSL https://get.docker.com -o get-docker.sh"
    echo "  sudo sh get-docker.sh"
    echo ""
    echo "CentOS/RHEL:"
    echo "  sudo yum install -y docker"
    echo "  sudo systemctl start docker"
    echo "  sudo systemctl enable docker"
    exit 1
fi

# 检查Docker是否运行
if ! docker info &> /dev/null; then
    echo "❌ Docker服务未运行，尝试启动..."
    sudo systemctl start docker
    sleep 5

    if ! docker info &> /dev/null; then
        echo "❌ Docker服务启动失败，请手动启动"
        exit 1
    fi
fi

echo "✅ Docker环境检查通过"
echo ""

# 查找镜像文件（优先使用2.0.0版本）
image_file=""
if [ -f "jar-decompiler-service-2.0.0.tar.gz" ]; then
    image_file="jar-decompiler-service-2.0.0.tar.gz"
    echo "📦 找到压缩镜像文件: $image_file (v2.0.0)"
elif [ -f "jar-decompiler-service-2.0.0.tar" ]; then
    image_file="jar-decompiler-service-2.0.0.tar"
    echo "📦 找到镜像文件: $image_file (v2.0.0)"
elif [ -f "docker-export-v2/jar-decompiler-service-2.0.0.tar.gz" ]; then
    image_file="docker-export-v2/jar-decompiler-service-2.0.0.tar.gz"
    echo "📦 找到压缩镜像文件: $image_file (v2.0.0)"
elif [ -f "docker-export-v2/jar-decompiler-service-2.0.0.tar" ]; then
    image_file="docker-export-v2/jar-decompiler-service-2.0.0.tar"
    echo "📦 找到镜像文件: $image_file (v2.0.0)"
elif [ -f "jar-decompiler-service-1.0.0.tar.gz" ]; then
    image_file="jar-decompiler-service-1.0.0.tar.gz"
    echo "📦 找到压缩镜像文件: $image_file (v1.0.0)"
elif [ -f "jar-decompiler-service-1.0.0.tar" ]; then
    image_file="jar-decompiler-service-1.0.0.tar"
    echo "📦 找到镜像文件: $image_file (v1.0.0)"
elif [ -f "docker-export/jar-decompiler-service-1.0.0.tar.gz" ]; then
    image_file="docker-export/jar-decompiler-service-1.0.0.tar.gz"
    echo "📦 找到压缩镜像文件: $image_file (v1.0.0)"
elif [ -f "docker-export/jar-decompiler-service-1.0.0.tar" ]; then
    image_file="docker-export/jar-decompiler-service-1.0.0.tar"
    echo "📦 找到镜像文件: $image_file (v1.0.0)"
else
    echo "❌ 未找到镜像文件，请确保以下文件之一存在:"
    echo "   - jar-decompiler-service-2.0.0.tar.gz (推荐)"
    echo "   - jar-decompiler-service-2.0.0.tar"
    echo "   - docker-export-v2/jar-decompiler-service-2.0.0.tar.gz"
    echo "   - docker-export-v2/jar-decompiler-service-2.0.0.tar"
    echo "   - jar-decompiler-service-1.0.0.tar.gz (旧版本)"
    echo "   - jar-decompiler-service-1.0.0.tar"
    exit 1
fi

echo ""
echo "📊 文件信息:"
ls -lh "$image_file"
echo ""

# 解压缩（如果需要）
if [[ "$image_file" == *.gz ]]; then
    echo "🗜️  解压缩镜像文件..."
    gunzip -k "$image_file"
    image_file="${image_file%.gz}"
    echo "✅ 解压完成: $image_file"
    echo ""
fi

# 导入Docker镜像
echo "📥 导入Docker镜像..."
docker load -i "$image_file"

if [ $? -eq 0 ]; then
    echo "✅ 镜像导入成功！"
else
    echo "❌ 镜像导入失败！"
    exit 1
fi

echo ""
echo "📊 已导入的镜像:"
docker images jar-decompiler-service
echo ""

# 停止并删除已存在的容器
echo "🧹 清理已存在的容器..."
docker stop jar-decompiler-service 2>/dev/null || true
docker rm jar-decompiler-service 2>/dev/null || true
echo ""

# 启动容器
echo "🚀 启动JAR反编译服务..."
docker run -d \
    --name jar-decompiler-service \
    --restart unless-stopped \
    -p 8080:8080 \
    -e SPRING_PROFILES_ACTIVE=docker \
    -e TZ=Asia/Shanghai \
    -e LANG=C.UTF-8 \
    -e LC_ALL=C.UTF-8 \
    -e JAVA_OPTS="-Xmx2g -Xms1g -XX:+UseG1GC -XX:+UseContainerSupport -Dfile.encoding=UTF-8 -Dsun.jnu.encoding=UTF-8" \
    -v jar_decompiler_temp:/app/temp \
    -v jar_decompiler_logs:/app/logs \
    $(docker images jar-decompiler-service --format "{{.Repository}}:{{.Tag}}" | head -1)

if [ $? -eq 0 ]; then
    echo "✅ 容器启动成功！"
else
    echo "❌ 容器启动失败！"
    exit 1
fi

echo ""
echo "⏳ 等待服务启动..."
sleep 15

# 检查服务状态
echo "🔍 检查服务状态..."
for i in {1..30}; do
    if curl -s http://localhost:8080/api/health > /dev/null 2>&1; then
        echo "✅ 服务启动成功！"
        echo ""
        echo "📊 容器状态:"
        docker ps | grep jar-decompiler-service
        echo ""
        echo "🌐 访问地址:"
        echo "   本地访问: http://localhost:8080"
        echo "   网络访问: http://$(hostname -I | awk '{print $1}'):8080"
        echo ""
        echo "📝 管理命令:"
        echo "   查看日志: docker logs jar-decompiler-service"
        echo "   停止服务: docker stop jar-decompiler-service"
        echo "   重启服务: docker restart jar-decompiler-service"
        echo "   删除容器: docker rm jar-decompiler-service"
        echo ""
        echo "🎉 部署完成！"
        break
    else
        echo "⏳ 等待服务启动... ($i/30)"
        sleep 2
    fi
done

if [ $i -eq 30 ]; then
    echo "⚠️  服务启动超时，请检查日志:"
    echo "   docker logs jar-decompiler-service"
    echo ""
    echo "📊 容器状态:"
    docker ps -a | grep jar-decompiler-service
fi
