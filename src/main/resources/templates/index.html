<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="#{app.title}">JAR Decompiler Service</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .upload-area {
            border: 2px dashed #007bff;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            background-color: #f8f9fa;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #0056b3;
            background-color: #e9ecef;
        }
        .upload-area.dragover {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .file-icon {
            font-size: 3rem;
            color: #007bff;
            margin-bottom: 20px;
        }
        .feature-card {
            transition: transform 0.2s;
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="text-center mb-5">
                    <h1 class="display-4 text-primary">
                        <i class="fas fa-file-archive"></i> <span th:text="#{app.title}">JAR Decompiler Service</span>
                    </h1>
                    <p class="lead text-muted" th:text="#{app.subtitle}">Upload your JAR file and get the decompiled source code instantly</p>

                    <!-- Language Selector -->
                    <div class="mt-3">
                        <div class="btn-group" role="group">
                            <a th:href="@{/(lang=en)}" class="btn btn-outline-secondary btn-sm">🇺🇸 English</a>
                            <a th:href="@{/(lang=zh)}" class="btn btn-outline-secondary btn-sm">🇨🇳 中文</a>
                            <a th:href="@{/(lang=de)}" class="btn btn-outline-secondary btn-sm">🇩🇪 Deutsch</a>
                            <a th:href="@{/(lang=fr)}" class="btn btn-outline-secondary btn-sm">🇫🇷 Français</a>
                        </div>
                    </div>
                </div>

                <!-- Error/Success Messages -->
                <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle"></i> <span th:text="${error}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>

                <div th:if="${success}" class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle"></i> <span th:text="${success}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>

                <!-- Upload Form -->
                <div class="card shadow">
                    <div class="card-body">
                        <form action="/upload" method="post" enctype="multipart/form-data" id="uploadForm">
                            <div class="upload-area" id="uploadArea">
                                <div class="file-icon">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                </div>
                                <h4 th:text="#{upload.title}">Drag & Drop your JAR file here</h4>
                                <p class="text-muted" th:text="#{upload.subtitle}">or click to browse</p>
                                <input type="file" name="jarFile" id="jarFile" accept=".jar" class="d-none" required>
                                <div id="fileInfo" class="mt-3" style="display: none;">
                                    <div class="alert alert-info">
                                        <i class="fas fa-file-archive"></i>
                                        <span id="fileName"></span>
                                        <span id="fileSize" class="text-muted"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="text-center mt-4">
                                <button type="submit" class="btn btn-primary btn-lg" id="submitBtn" disabled>
                                    <i class="fas fa-cogs"></i> <span th:text="#{upload.button}">Decompile JAR</span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Features -->
                <div class="row mt-5">
                    <div class="col-md-4 mb-3">
                        <div class="card feature-card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-rocket fa-2x text-primary mb-3"></i>
                                <h5 th:text="#{feature.fast.title}">Fast Processing</h5>
                                <p class="text-muted" th:text="#{feature.fast.desc}">Quick decompilation using CFR decompiler</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="card feature-card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-download fa-2x text-success mb-3"></i>
                                <h5 th:text="#{feature.download.title}">Download Project</h5>
                                <p class="text-muted" th:text="#{feature.download.desc}">Get organized source code as a ZIP file</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="card feature-card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-shield-alt fa-2x text-warning mb-3"></i>
                                <h5 th:text="#{feature.secure.title}">Secure</h5>
                                <p class="text-muted" th:text="#{feature.secure.desc}">Files are processed temporarily and deleted</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('jarFile');
        const submitBtn = document.getElementById('submitBtn');
        const fileInfo = document.getElementById('fileInfo');
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');

        // Click to upload
        uploadArea.addEventListener('click', () => fileInput.click());

        // File selection
        fileInput.addEventListener('change', handleFileSelect);

        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                handleFileSelect();
            }
        });

        function handleFileSelect() {
            const file = fileInput.files[0];
            if (file) {
                if (file.name.toLowerCase().endsWith('.jar')) {
                    fileName.textContent = file.name;
                    fileSize.textContent = `(${formatFileSize(file.size)})`;
                    fileInfo.style.display = 'block';
                    submitBtn.disabled = false;
                } else {
                    alert('Please select a valid JAR file.');
                    fileInput.value = '';
                    fileInfo.style.display = 'none';
                    submitBtn.disabled = true;
                }
            }
        }

        function formatFileSize(bytes) {
            if (bytes < 1024) return bytes + ' B';
            if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
            return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
        }

        // Form submission
        document.getElementById('uploadForm').addEventListener('submit', function() {
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
            submitBtn.disabled = true;
        });
    </script>
</body>
</html>
