<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="#{result.title} + ' - ' + #{app.title}">Decompilation Result - JAR Decompiler Service</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/themes/prism.min.css" rel="stylesheet">
    <style>
        .result-header {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 2rem 0;
        }
        .file-tree {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            white-space: pre-line;
            max-height: 500px;
            overflow-y: auto;
        }
        .action-card {
            transition: transform 0.2s;
        }
        .action-card:hover {
            transform: translateY(-2px);
        }
        .stats-card {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }
    </style>
</head>
<body>
    <div class="result-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="fas fa-check-circle"></i> <span th:text="#{result.title}">Decompilation Successful!</span></h1>
                    <p class="lead mb-0" th:text="#{result.subtitle}">Your JAR file has been successfully decompiled</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="/" class="btn btn-light btn-lg">
                        <i class="fas fa-upload"></i> <span th:text="#{nav.upload.another}">Upload Another</span>
                    </a>
                    <!-- Language Selector -->
                    <div class="mt-2">
                        <div class="btn-group btn-group-sm" role="group">
                            <a th:href="@{/upload(lang=en)}" class="btn btn-outline-light">🇺🇸</a>
                            <a th:href="@{/upload(lang=zh)}" class="btn btn-outline-light">🇨🇳</a>
                            <a th:href="@{/upload(lang=de)}" class="btn btn-outline-light">🇩🇪</a>
                            <a th:href="@{/upload(lang=fr)}" class="btn btn-outline-light">🇫🇷</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container mt-4">
        <!-- File Information -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle"></i> <span th:text="#{result.file.info}">File Information</span></h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <strong th:text="#{result.file.name} + ':'">File Name:</strong> <span th:text="${fileName}"></span>
                            </div>
                            <div class="col-md-6">
                                <strong th:text="#{result.file.size} + ':'">File Size:</strong> <span th:text="${fileSize}"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h3><i class="fas fa-file-code"></i></h3>
                        <h5 th:text="#{result.ready.title}">Ready to Download</h5>
                        <p class="mb-0" th:text="#{result.ready.subtitle}">Source code extracted</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card action-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-download fa-3x text-primary mb-3"></i>
                        <h5 th:text="#{action.download.title}">Download Project</h5>
                        <p class="text-muted" th:text="#{action.download.desc}">Download the complete decompiled project as a ZIP file</p>
                        <form action="/download" method="post" id="downloadForm">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-download"></i> <span th:text="#{action.download.button}">Download ZIP</span>
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card action-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-eye fa-3x text-success mb-3"></i>
                        <h5 th:text="#{action.view.title}">View Structure</h5>
                        <p class="text-muted" th:text="#{action.view.desc}">Browse the decompiled project structure below</p>
                        <button class="btn btn-success btn-lg" onclick="scrollToStructure()">
                            <i class="fas fa-eye"></i> <span th:text="#{action.view.button}">View Structure</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Project Structure -->
        <div class="card" id="structureSection">
            <div class="card-header">
                <h5><i class="fas fa-folder-tree"></i> <span th:text="#{structure.title}">Project Structure</span></h5>
            </div>
            <div class="card-body">
                <div class="file-tree" th:text="${projectStructure}">
                    <!-- Project structure will be displayed here -->
                </div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="card mt-4">
            <div class="card-header">
                <h5><i class="fas fa-lightbulb"></i> <span th:text="#{instructions.title}">Next Steps</span></h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="text-center">
                            <i class="fas fa-download fa-2x text-primary mb-2"></i>
                            <h6 th:text="#{instructions.download.title}">1. Download</h6>
                            <p class="text-muted small" th:text="#{instructions.download.desc}">Download the ZIP file containing all decompiled source files</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <i class="fas fa-folder-open fa-2x text-warning mb-2"></i>
                            <h6 th:text="#{instructions.extract.title}">2. Extract</h6>
                            <p class="text-muted small" th:text="#{instructions.extract.desc}">Extract the ZIP file to your desired location</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <i class="fas fa-code fa-2x text-success mb-2"></i>
                            <h6 th:text="#{instructions.import.title}">3. Import</h6>
                            <p class="text-muted small" th:text="#{instructions.import.desc}">Import the project into your favorite IDE</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer class="mt-5 py-4 bg-light">
        <div class="container text-center">
            <p class="text-muted mb-0">
                <i class="fas fa-shield-alt"></i>
                <span th:text="#{footer.security}">Files are processed securely and temporarily. No data is stored permanently.</span>
            </p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function scrollToStructure() {
            document.getElementById('structureSection').scrollIntoView({
                behavior: 'smooth'
            });
        }

        // Auto-scroll to structure if it's the main content
        window.addEventListener('load', function() {
            setTimeout(() => {
                scrollToStructure();
            }, 1000);
        });
    </script>
</body>
</html>
