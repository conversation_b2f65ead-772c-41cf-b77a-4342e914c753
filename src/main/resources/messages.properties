# Default (English) messages
app.title=JAR Decompiler Service
app.subtitle=Upload your JAR file and get the decompiled source code instantly

# Navigation
nav.upload.another=Upload Another
nav.language=Language

# Upload page
upload.title=Drag & Drop your JAR file here
upload.subtitle=or click to browse
upload.button=Decompile JAR
upload.processing=Processing...

# Features
feature.fast.title=Fast Processing
feature.fast.desc=Quick decompilation using CFR decompiler
feature.download.title=Download Project
feature.download.desc=Get organized source code as a ZIP file
feature.secure.title=Secure
feature.secure.desc=Files are processed temporarily and deleted

# Result page
result.title=Decompilation Successful!
result.subtitle=Your JAR file has been successfully decompiled
result.file.info=File Information
result.file.name=File Name
result.file.size=File Size
result.ready.title=Ready to Download
result.ready.subtitle=Source code extracted

# Actions
action.download.title=Download Project
action.download.desc=Download the complete decompiled project as a ZIP file
action.download.button=Download ZIP
action.view.title=View Structure
action.view.desc=Browse the decompiled project structure below
action.view.button=View Structure

# Project structure
structure.title=Project Structure
structure.decompiled=Decompiled Project Structure:

# Instructions
instructions.title=Next Steps
instructions.download.title=1. Download
instructions.download.desc=Download the ZIP file containing all decompiled source files
instructions.extract.title=2. Extract
instructions.extract.desc=Extract the ZIP file to your desired location
instructions.import.title=3. Import
instructions.import.desc=Import the project into your favorite IDE

# Footer
footer.security=Files are processed securely and temporarily. No data is stored permanently.

# Error messages
error.no.file=Please select a JAR file to upload.
error.invalid.file=Please upload a valid JAR file.
error.decompile.failed=Error decompiling JAR file: {0}
error.download.failed=Failed to download the project.

# Success messages
success.upload=File uploaded successfully.
success.decompile=Decompilation completed successfully.
