# Docker环境专用配置
# Server Configuration
server.port=8080
server.servlet.context-path=/

# File Upload Configuration
spring.servlet.multipart.max-file-size=100MB
spring.servlet.multipart.max-request-size=100MB
spring.servlet.multipart.enabled=true

# Thymeleaf Configuration
spring.thymeleaf.cache=true
spring.thymeleaf.prefix=classpath:/templates/
spring.thymeleaf.suffix=.html

# Logging Configuration for Docker
logging.level.com.jardecompiler=INFO
logging.level.org.benf.cfr=WARN
logging.level.org.springframework.web=INFO
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n

# Application Information
spring.application.name=JAR Decompiler Service

# Internationalization
spring.messages.basename=messages
spring.messages.encoding=UTF-8
spring.messages.cache-duration=3600

# Docker specific settings
# 临时文件目录
java.io.tmpdir=/app/temp

# 禁用开发工具
spring.devtools.restart.enabled=false
spring.devtools.livereload.enabled=false
