# Server Configuration
server.port=8080
server.servlet.context-path=/

# File Upload Configuration
spring.servlet.multipart.max-file-size=100MB
spring.servlet.multipart.max-request-size=100MB
spring.servlet.multipart.enabled=true

# Thymeleaf Configuration
spring.thymeleaf.cache=false
spring.thymeleaf.prefix=classpath:/templates/
spring.thymeleaf.suffix=.html

# Logging Configuration
logging.level.com.jardecompiler=INFO
logging.level.org.benf.cfr=WARN

# Application Information
spring.application.name=JAR Decompiler Service

# Internationalization
spring.messages.basename=messages
spring.messages.encoding=UTF-8
spring.messages.cache-duration=3600
