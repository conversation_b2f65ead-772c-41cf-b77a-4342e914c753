# Chinese messages
app.title=JAR\u53cd\u7f16\u8bd1\u670d\u52a1
app.subtitle=\u4e0a\u4f20\u60a8\u7684JAR\u6587\u4ef6\uff0c\u7acb\u5373\u83b7\u53d6\u53cd\u7f16\u8bd1\u7684\u6e90\u4ee3\u7801

# Navigation
nav.upload.another=\u4e0a\u4f20\u5176\u4ed6\u6587\u4ef6
nav.language=\u8bed\u8a00

# Upload page
upload.title=\u62d6\u62fd\u60a8\u7684JAR\u6587\u4ef6\u5230\u8fd9\u91cc
upload.subtitle=\u6216\u70b9\u51fb\u6d4f\u89c8
upload.button=\u53cd\u7f16\u8bd1JAR
upload.processing=\u5904\u7406\u4e2d...

# Features
feature.fast.title=\u5feb\u901f\u5904\u7406
feature.fast.desc=\u4f7f\u7528CFR\u53cd\u7f16\u8bd1\u5668\u5feb\u901f\u53cd\u7f16\u8bd1
feature.download.title=\u4e0b\u8f7d\u9879\u76ee
feature.download.desc=\u83b7\u53d6\u7ec4\u7ec7\u826f\u597d\u7684\u6e90\u4ee3\u7801ZIP\u6587\u4ef6
feature.secure.title=\u5b89\u5168
feature.secure.desc=\u6587\u4ef6\u4e34\u65f6\u5904\u7406\u540e\u81ea\u52a8\u5220\u9664

# Result page
result.title=\u53cd\u7f16\u8bd1\u6210\u529f\uff01
result.subtitle=\u60a8\u7684JAR\u6587\u4ef6\u5df2\u6210\u529f\u53cd\u7f16\u8bd1
result.file.info=\u6587\u4ef6\u4fe1\u606f
result.file.name=\u6587\u4ef6\u540d
result.file.size=\u6587\u4ef6\u5927\u5c0f
result.ready.title=\u51c6\u5907\u4e0b\u8f7d
result.ready.subtitle=\u6e90\u4ee3\u7801\u5df2\u63d0\u53d6

# Actions
action.download.title=\u4e0b\u8f7d\u9879\u76ee
action.download.desc=\u4e0b\u8f7d\u5b8c\u6574\u7684\u53cd\u7f16\u8bd1\u9879\u76eeZIP\u6587\u4ef6
action.download.button=\u4e0b\u8f7dZIP
action.view.title=\u67e5\u770b\u7ed3\u6784
action.view.desc=\u6d4f\u89c8\u4e0b\u9762\u7684\u53cd\u7f16\u8bd1\u9879\u76ee\u7ed3\u6784
action.view.button=\u67e5\u770b\u7ed3\u6784

# Project structure
structure.title=\u9879\u76ee\u7ed3\u6784
structure.decompiled=\u53cd\u7f16\u8bd1\u9879\u76ee\u7ed3\u6784\uff1a

# Instructions
instructions.title=\u4e0b\u4e00\u6b65
instructions.download.title=1. \u4e0b\u8f7d
instructions.download.desc=\u4e0b\u8f7d\u5305\u542b\u6240\u6709\u53cd\u7f16\u8bd1\u6e90\u6587\u4ef6\u7684ZIP\u6587\u4ef6
instructions.extract.title=2. \u89e3\u538b
instructions.extract.desc=\u5c06ZIP\u6587\u4ef6\u89e3\u538b\u5230\u60a8\u60f3\u8981\u7684\u4f4d\u7f6e
instructions.import.title=3. \u5bfc\u5165
instructions.import.desc=\u5c06\u9879\u76ee\u5bfc\u5165\u60a8\u559c\u6b22\u7684IDE

# Footer
footer.security=\u6587\u4ef6\u5b89\u5168\u4e34\u65f6\u5904\u7406\uff0c\u4e0d\u4f1a\u6c38\u4e45\u5b58\u50a8\u6570\u636e\u3002

# Error messages
error.no.file=\u8bf7\u9009\u62e9\u8981\u4e0a\u4f20\u7684JAR\u6587\u4ef6\u3002
error.invalid.file=\u8bf7\u4e0a\u4f20\u6709\u6548\u7684JAR\u6587\u4ef6\u3002
error.decompile.failed=\u53cd\u7f16\u8bd1JAR\u6587\u4ef6\u65f6\u51fa\u9519\uff1a{0}
error.download.failed=\u4e0b\u8f7d\u9879\u76ee\u5931\u8d25\u3002

# Success messages
success.upload=\u6587\u4ef6\u4e0a\u4f20\u6210\u529f\u3002
success.decompile=\u53cd\u7f16\u8bd1\u5b8c\u6210\u3002
