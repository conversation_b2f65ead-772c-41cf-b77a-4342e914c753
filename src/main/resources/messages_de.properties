# German messages
app.title=JAR-Dekompilierungsdienst
app.subtitle=Laden Sie Ihre JAR-Datei hoch und erhalten Sie sofort den dekompilierten Quellcode

# Navigation
nav.upload.another=Weitere hochladen
nav.language=Sprache

# Upload page
upload.title=Ziehen Sie Ihre JAR-Datei hierher
upload.subtitle=oder klicken Sie zum Durchsuchen
upload.button=JAR dekompilieren
upload.processing=Verarbeitung...

# Features
feature.fast.title=Schnelle Verarbeitung
feature.fast.desc=Schnelle Dekompilierung mit CFR-Dekompilierer
feature.download.title=Projekt herunterladen
feature.download.desc=Organisierten Quellcode als ZIP-Datei erhalten
feature.secure.title=Sicher
feature.secure.desc=Dateien werden temporär verarbeitet und gelöscht

# Result page
result.title=Dekompilierung erfolgreich!
result.subtitle=Ihre JAR-Datei wurde erfolgreich dekompiliert
result.file.info=Dateiinformationen
result.file.name=Dateiname
result.file.size=Dateigr\u00f6\u00dfe
result.ready.title=Bereit zum Download
result.ready.subtitle=Quellcode extrahiert

# Actions
action.download.title=Projekt herunterladen
action.download.desc=Das vollst\u00e4ndige dekompilierte Projekt als ZIP-Datei herunterladen
action.download.button=ZIP herunterladen
action.view.title=Struktur anzeigen
action.view.desc=Die dekompilierte Projektstruktur unten durchsuchen
action.view.button=Struktur anzeigen

# Project structure
structure.title=Projektstruktur
structure.decompiled=Dekompilierte Projektstruktur:

# Instructions
instructions.title=N\u00e4chste Schritte
instructions.download.title=1. Herunterladen
instructions.download.desc=Die ZIP-Datei mit allen dekompilierten Quelldateien herunterladen
instructions.extract.title=2. Extrahieren
instructions.extract.desc=Die ZIP-Datei an den gew\u00fcnschten Ort extrahieren
instructions.import.title=3. Importieren
instructions.import.desc=Das Projekt in Ihre bevorzugte IDE importieren

# Footer
footer.security=Dateien werden sicher und tempor\u00e4r verarbeitet. Keine Daten werden dauerhaft gespeichert.

# Error messages
error.no.file=Bitte w\u00e4hlen Sie eine JAR-Datei zum Hochladen aus.
error.invalid.file=Bitte laden Sie eine g\u00fcltige JAR-Datei hoch.
error.decompile.failed=Fehler beim Dekompilieren der JAR-Datei: {0}
error.download.failed=Projekt-Download fehlgeschlagen.

# Success messages
success.upload=Datei erfolgreich hochgeladen.
success.decompile=Dekompilierung erfolgreich abgeschlossen.
