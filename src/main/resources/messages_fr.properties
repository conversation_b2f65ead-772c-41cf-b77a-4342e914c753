# French messages
app.title=Service de D\u00e9compilation JAR
app.subtitle=T\u00e9l\u00e9chargez votre fichier JAR et obtenez instantan\u00e9ment le code source d\u00e9compil\u00e9

# Navigation
nav.upload.another=T\u00e9l\u00e9charger un autre
nav.language=Langue

# Upload page
upload.title=Glissez-d\u00e9posez votre fichier JAR ici
upload.subtitle=ou cliquez pour parcourir
upload.button=D\u00e9compiler JAR
upload.processing=Traitement...

# Features
feature.fast.title=Traitement Rapide
feature.fast.desc=D\u00e9compilation rapide avec le d\u00e9compilateur CFR
feature.download.title=T\u00e9l\u00e9charger le Projet
feature.download.desc=Obtenez le code source organis\u00e9 sous forme de fichier ZIP
feature.secure.title=S\u00e9curis\u00e9
feature.secure.desc=Les fichiers sont trait\u00e9s temporairement et supprim\u00e9s

# Result page
result.title=D\u00e9compilation R\u00e9ussie !
result.subtitle=Votre fichier JAR a \u00e9t\u00e9 d\u00e9compil\u00e9 avec succ\u00e8s
result.file.info=Informations sur le Fichier
result.file.name=Nom du Fichier
result.file.size=Taille du Fichier
result.ready.title=Pr\u00eat \u00e0 T\u00e9l\u00e9charger
result.ready.subtitle=Code source extrait

# Actions
action.download.title=T\u00e9l\u00e9charger le Projet
action.download.desc=T\u00e9l\u00e9chargez le projet d\u00e9compil\u00e9 complet sous forme de fichier ZIP
action.download.button=T\u00e9l\u00e9charger ZIP
action.view.title=Voir la Structure
action.view.desc=Parcourez la structure du projet d\u00e9compil\u00e9 ci-dessous
action.view.button=Voir la Structure

# Project structure
structure.title=Structure du Projet
structure.decompiled=Structure du Projet D\u00e9compil\u00e9 :

# Instructions
instructions.title=\u00c9tapes Suivantes
instructions.download.title=1. T\u00e9l\u00e9charger
instructions.download.desc=T\u00e9l\u00e9chargez le fichier ZIP contenant tous les fichiers sources d\u00e9compil\u00e9s
instructions.extract.title=2. Extraire
instructions.extract.desc=Extrayez le fichier ZIP \u00e0 l'emplacement souhait\u00e9
instructions.import.title=3. Importer
instructions.import.desc=Importez le projet dans votre IDE pr\u00e9f\u00e9r\u00e9

# Footer
footer.security=Les fichiers sont trait\u00e9s de mani\u00e8re s\u00e9curis\u00e9e et temporaire. Aucune donn\u00e9e n'est stock\u00e9e de fa\u00e7on permanente.

# Error messages
error.no.file=Veuillez s\u00e9lectionner un fichier JAR \u00e0 t\u00e9l\u00e9charger.
error.invalid.file=Veuillez t\u00e9l\u00e9charger un fichier JAR valide.
error.decompile.failed=Erreur lors de la d\u00e9compilation du fichier JAR : {0}
error.download.failed=\u00c9chec du t\u00e9l\u00e9chargement du projet.

# Success messages
success.upload=Fichier t\u00e9l\u00e9charg\u00e9 avec succ\u00e8s.
success.decompile=D\u00e9compilation termin\u00e9e avec succ\u00e8s.
