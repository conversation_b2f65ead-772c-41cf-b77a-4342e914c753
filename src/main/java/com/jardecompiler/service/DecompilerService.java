package com.jardecompiler.service;

import org.benf.cfr.reader.api.CfrDriver;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.apache.commons.io.FileUtils;

import java.io.*;
import java.nio.file.*;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * JAR反编译服务 - 实现JAR文件的反编译功能，生成可下载的项目源代码。
 * 
 * **作者**: huruifeng
 * **开源协议**: MIT License
 */
@Service
public class DecompilerService {

    public String decompileJar(MultipartFile jarFile) throws Exception {
        // Create temporary directories
        Path tempDir = Files.createTempDirectory("jar-decompiler");
        Path jarPath = tempDir.resolve(jarFile.getOriginalFilename());
        Path outputDir = tempDir.resolve("decompiled");

        try {
            // Save uploaded file
            Files.copy(jarFile.getInputStream(), jarPath);
            Files.createDirectories(outputDir);

            // Decompile using CFR
            decompileWithCFR(jarPath.toString(), outputDir.toString());

            // Create project structure
            String projectStructure = createProjectStructure(outputDir);

            return projectStructure;

        } finally {
            // Clean up temporary files
            FileUtils.deleteDirectory(tempDir.toFile());
        }
    }

    private void decompileWithCFR(String jarPath, String outputDir) {
        try {
            Map<String, String> options = new HashMap<>();
            options.put("outputdir", outputDir);
            options.put("caseinsensitivefs", "true");
            options.put("silent", "false");
            options.put("recover", "true");
            options.put("eclipse", "true");
            options.put("override", "true");

            // Use CFR's built-in file output
            CfrDriver driver = new CfrDriver.Builder()
                .withOptions(options)
                .build();

            driver.analyse(Arrays.asList(jarPath));

        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("Failed to decompile JAR file", e);
        }
    }

    private String createProjectStructure(Path outputDir) throws IOException {
        StringBuilder structure = new StringBuilder();
        structure.append("Decompiled Project Structure:\n");
        structure.append("============================\n\n");

        Files.walk(outputDir)
            .filter(Files::isRegularFile)
            .filter(path -> path.toString().endsWith(".java"))
            .sorted()
            .forEach(path -> {
                Path relativePath = outputDir.relativize(path);
                structure.append("📁 ").append(relativePath.toString()).append("\n");
            });

        return structure.toString();
    }

    public byte[] createProjectZip(MultipartFile jarFile) throws Exception {
        Path tempDir = Files.createTempDirectory("jar-decompiler");
        Path jarPath = tempDir.resolve(jarFile.getOriginalFilename());
        Path outputDir = tempDir.resolve("decompiled");

        try {
            // Save uploaded file and decompile
            Files.copy(jarFile.getInputStream(), jarPath);
            Files.createDirectories(outputDir);
            decompileWithCFR(jarPath.toString(), outputDir.toString());

            // Create ZIP file
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            try (ZipOutputStream zos = new ZipOutputStream(baos)) {
                Files.walk(outputDir)
                    .filter(Files::isRegularFile)
                    .forEach(file -> {
                        try {
                            Path relativePath = outputDir.relativize(file);
                            ZipEntry zipEntry = new ZipEntry(relativePath.toString());
                            zos.putNextEntry(zipEntry);
                            Files.copy(file, zos);
                            zos.closeEntry();
                        } catch (IOException e) {
                            throw new RuntimeException(e);
                        }
                    });
            }

            return baos.toByteArray();

        } finally {
            FileUtils.deleteDirectory(tempDir.toFile());
        }
    }
}
