package com.jardecompiler.util;

import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;

public class SessionMultipartFile implements MultipartFile {
    
    private final String name;
    private final byte[] content;
    
    public SessionMultipartFile(String name, byte[] content) {
        this.name = name;
        this.content = content;
    }
    
    @Override
    public String getName() {
        return "jarFile";
    }
    
    @Override
    public String getOriginalFilename() {
        return name;
    }
    
    @Override
    public String getContentType() {
        return "application/java-archive";
    }
    
    @Override
    public boolean isEmpty() {
        return content == null || content.length == 0;
    }
    
    @Override
    public long getSize() {
        return content != null ? content.length : 0;
    }
    
    @Override
    public byte[] getBytes() throws IOException {
        return content;
    }
    
    @Override
    public InputStream getInputStream() throws IOException {
        return new ByteArrayInputStream(content);
    }
    
    @Override
    public void transferTo(File dest) throws IOException, IllegalStateException {
        throw new UnsupportedOperationException("transferTo not supported for SessionMultipartFile");
    }
}
