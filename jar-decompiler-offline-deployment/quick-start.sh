#!/bin/bash

echo "🚀 JAR反编译服务 - 快速启动"
echo "=========================="
echo ""
echo "请选择部署方式:"
echo "1. <PERSON><PERSON>部署（推荐）"
echo "2. JAR直接部署"
echo ""
read -p "请输入选择 (1 或 2): " choice

case $choice in
    1)
        echo ""
        echo "🐳 使用Docker部署..."
        chmod +x deploy-offline.sh
        ./deploy-offline.sh
        ;;
    2)
        echo ""
        echo "☕ 使用JAR直接部署..."
        if [ -d "jar-deployment" ]; then
            cd jar-deployment
            echo "启动服务..."
            java -jar jar-decompiler-service-1.0.0.jar --spring.config.location=application-docker.properties
        else
            echo "❌ JAR部署文件不存在"
            exit 1
        fi
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac
