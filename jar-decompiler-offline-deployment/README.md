# JAR反编译服务 - 离线部署包

## 📋 包含文件

- `jar-decompiler-service-1.0.0.tar.gz` - Docker镜像文件
- `deploy-offline.sh` - 离线部署脚本
- `docker-compose.yml` - Docker Compose配置
- `quick-start.sh` - 快速启动脚本
- `DEPLOYMENT_GUIDE.md` - 详细部署指南
- `jar-deployment/` - JAR直接部署文件（备用）

## 🚀 快速部署

### 方法一：一键部署
```bash
chmod +x quick-start.sh
./quick-start.sh
```

### 方法二：Docker部署
```bash
chmod +x deploy-offline.sh
./deploy-offline.sh
```

### 方法三：JAR部署
```bash
cd jar-deployment
java -jar jar-decompiler-service-1.0.0.jar --spring.config.location=application-docker.properties
```

## 🌐 访问服务

部署成功后访问: http://服务器IP:8080

## 📖 详细说明

请查看 `DEPLOYMENT_GUIDE.md` 获取完整部署指南。
