# JAR反编译服务 - 离线部署指南

**作者**: huruifeng  
**开源协议**: MIT License

## 📋 概述

本指南介绍如何将JAR反编译服务部署到离线服务器环境中。

## 🎯 部署方式

### 方式一：Docker镜像部署（推荐）

#### 1. 导出Docker镜像

在有网络的开发环境中：

```bash
# 给脚本添加执行权限
chmod +x export-docker-image.sh

# 导出Docker镜像
./export-docker-image.sh
```

导出完成后，会在 `docker-export/` 目录下生成：
- `jar-decompiler-service-1.0.0.tar.gz` - 压缩的Docker镜像文件

#### 2. 传输文件到目标服务器

将以下文件复制到目标服务器：
```
jar-decompiler-service-1.0.0.tar.gz
docker-compose.yml
deploy-offline.sh
DEPLOYMENT_GUIDE.md
```

#### 3. 在目标服务器部署

```bash
# 给脚本添加执行权限
chmod +x deploy-offline.sh

# 执行离线部署
./deploy-offline.sh
```

### 方式二：JAR文件直接部署

#### 1. 准备文件

复制以下文件到目标服务器：
```
target/jar-decompiler-service-1.0.0.jar
src/main/resources/application-docker.properties
```

#### 2. 安装Java环境

确保目标服务器安装了Java 17+：

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install openjdk-17-jre

# CentOS/RHEL
sudo yum install java-17-openjdk

# 验证安装
java -version
```

#### 3. 运行服务

```bash
# 直接运行
java -jar jar-decompiler-service-1.0.0.jar --spring.profiles.active=docker

# 或者后台运行
nohup java -jar jar-decompiler-service-1.0.0.jar --spring.profiles.active=docker > app.log 2>&1 &
```

## 🔧 配置说明

### 环境变量配置

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `SERVER_PORT` | 8080 | 服务端口 |
| `SPRING_PROFILES_ACTIVE` | docker | 配置文件 |
| `JAVA_OPTS` | -Xmx512m -Xms256m | JVM参数 |
| `TZ` | Asia/Shanghai | 时区设置 |

### 端口配置

- **Web服务**: 8080 (HTTP)
- **健康检查**: 8080/api/health

### 资源要求

- **最小内存**: 512MB
- **推荐内存**: 1GB
- **磁盘空间**: 1GB（用于临时文件）
- **CPU**: 1核心

## 🚀 验证部署

### 1. 检查服务状态

```bash
# Docker部署
docker ps | grep jar-decompiler-service
docker logs jar-decompiler-service

# JAR部署
ps aux | grep jar-decompiler-service
```

### 2. 健康检查

```bash
curl http://localhost:8080/api/health
# 期望输出: JAR Decompiler Service is running
```

### 3. Web界面测试

访问: `http://服务器IP:8080`

## 🛠 故障排除

### 常见问题

#### 1. 端口被占用
```bash
# 查看端口占用
netstat -tlnp | grep 8080
# 或
lsof -i :8080

# 修改端口
docker run -p 9090:8080 ...  # Docker
java -jar app.jar --server.port=9090  # JAR
```

#### 2. 内存不足
```bash
# 调整JVM内存
export JAVA_OPTS="-Xmx256m -Xms128m"
java $JAVA_OPTS -jar app.jar
```

#### 3. 权限问题
```bash
# 确保文件权限正确
chmod +x deploy-offline.sh
chmod 644 jar-decompiler-service-1.0.0.jar
```

#### 4. Docker服务未启动
```bash
# 启动Docker服务
sudo systemctl start docker
sudo systemctl enable docker
```

### 日志查看

```bash
# Docker部署
docker logs jar-decompiler-service
docker logs -f jar-decompiler-service  # 实时查看

# JAR部署
tail -f app.log
```

## 📊 监控和维护

### 服务管理

```bash
# Docker方式
docker start jar-decompiler-service    # 启动
docker stop jar-decompiler-service     # 停止
docker restart jar-decompiler-service  # 重启

# JAR方式（需要进程管理工具如systemd）
sudo systemctl start jar-decompiler
sudo systemctl stop jar-decompiler
sudo systemctl restart jar-decompiler
```

### 数据备份

临时文件会自动清理，无需特殊备份。如需保留日志：

```bash
# 备份Docker容器日志
docker logs jar-decompiler-service > backup-$(date +%Y%m%d).log
```

## 🔒 安全建议

1. **防火墙配置**: 只开放必要端口（8080）
2. **反向代理**: 建议使用Nginx等反向代理
3. **HTTPS**: 生产环境建议配置SSL证书
4. **访问控制**: 根据需要配置IP白名单

## 📞 技术支持

如遇到问题，请检查：
1. 系统日志
2. 应用日志
3. Docker日志（如适用）
4. 网络连接
5. 资源使用情况

---

**部署完成后，您可以通过浏览器访问服务进行JAR文件反编译操作！**
