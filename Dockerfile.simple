# 简单的Dockerfile - 使用预构建的JAR文件
FROM eclipse-temurin:17-jre-alpine

# 设置维护者信息
LABEL maintainer="huruifeng <<EMAIL>>"
LABEL description="JAR Decompiler Service - A web service to decompile JAR files to source code"
LABEL version="1.0.0"

# 注意：为了简化构建，跳过额外工具安装
# 如果需要curl等工具，可以在运行时安装

# 设置时区和字符编码
ENV TZ=Asia/Shanghai
ENV LANG=C.UTF-8
ENV LC_ALL=C.UTF-8

# 创建应用用户（安全最佳实践）
RUN addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup

# 设置工作目录
WORKDIR /app

# 创建必要的目录
RUN mkdir -p /app/temp /app/logs && \
    chown -R appuser:appgroup /app

# 复制JAR文件（需要先在本地构建）
COPY target/jar-decompiler-service-1.0.0.jar app.jar

# 修改文件所有者
RUN chown appuser:appgroup app.jar

# 切换到非root用户
USER appuser

# 暴露端口
EXPOSE 8080

# 设置JVM参数（包含字符编码）
ENV JAVA_OPTS="-Xmx512m -Xms256m -XX:+UseG1GC -XX:+UseContainerSupport -Dfile.encoding=UTF-8 -Dsun.jnu.encoding=UTF-8"

# 设置Spring Boot配置
ENV SPRING_PROFILES_ACTIVE=docker

# 健康检查（使用Java内置工具）
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD java -cp app.jar org.springframework.boot.loader.JarLauncher --help > /dev/null 2>&1 || exit 1

# 启动应用
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]
