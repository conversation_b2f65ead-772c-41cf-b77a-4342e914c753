#!/bin/bash

echo "🔧 测试文件上传大小限制修复"
echo "============================="
echo ""

# 检查服务状态
echo "1. 检查服务状态..."
response=$(curl -s http://localhost:8080/api/health)
if [ "$response" = "JAR Decompiler Service is running" ]; then
    echo "✅ 服务正常运行"
else
    echo "❌ 服务未运行，请先启动服务"
    exit 1
fi
echo ""

# 检查配置修复
echo "2. 检查配置修复..."
echo ""

echo "📋 application.properties 文件上传配置:"
grep -A10 "File Upload Configuration" src/main/resources/application.properties
echo ""

echo "📋 application-docker.properties 文件上传配置:"
grep -A10 "File Upload Configuration" src/main/resources/application-docker.properties
echo ""

# 检查新增的配置类
echo "3. 检查新增的配置类..."
echo ""

if [ -f "src/main/java/com/jardecompiler/config/FileUploadConfig.java" ]; then
    echo "✅ FileUploadConfig.java 已创建"
    echo "📋 配置内容:"
    grep -A5 "setMaxFileSize\|setMaxRequestSize" src/main/java/com/jardecompiler/config/FileUploadConfig.java
else
    echo "❌ FileUploadConfig.java 未找到"
fi
echo ""

if [ -f "src/main/java/com/jardecompiler/exception/GlobalExceptionHandler.java" ]; then
    echo "✅ GlobalExceptionHandler.java 已创建"
    echo "📋 异常处理:"
    grep -A3 "MaxUploadSizeExceededException" src/main/java/com/jardecompiler/exception/GlobalExceptionHandler.java
else
    echo "❌ GlobalExceptionHandler.java 未找到"
fi
echo ""

# 检查控制器修改
echo "4. 检查控制器文件大小验证..."
echo ""

if grep -q "maxSize.*524_288_000L" src/main/java/com/jardecompiler/controller/DecompilerController.java; then
    echo "✅ 控制器已添加500MB文件大小检查"
else
    echo "❌ 控制器文件大小检查未添加"
fi
echo ""

# 显示修复总结
echo "📋 修复总结:"
echo "============"
echo ""
echo "✅ 配置文件修复:"
echo "1. spring.servlet.multipart.max-file-size=500MB"
echo "2. spring.servlet.multipart.max-request-size=500MB"
echo "3. server.tomcat.max-swallow-size=500MB"
echo "4. server.tomcat.max-http-form-post-size=500MB"
echo ""
echo "✅ Java配置类:"
echo "1. FileUploadConfig - 程序化配置文件上传限制"
echo "2. GlobalExceptionHandler - 处理文件上传异常"
echo ""
echo "✅ 控制器增强:"
echo "1. 添加文件大小预检查"
echo "2. 友好的错误提示信息"
echo ""
echo "🎯 预期效果:"
echo "- 支持最大500MB的JAR文件上传"
echo "- 超过限制时显示友好错误信息"
echo "- 不再出现 MaxUploadSizeExceededException"
echo ""

# 创建测试文件信息
echo "📊 测试建议:"
echo "============"
echo ""
echo "1. 小文件测试 (< 500MB):"
echo "   - 应该正常上传和处理"
echo ""
echo "2. 大文件测试 (> 500MB):"
echo "   - 应该显示友好错误信息"
echo "   - 不应该出现系统异常"
echo ""
echo "3. 边界测试 (接近500MB):"
echo "   - 测试499MB文件应该成功"
echo "   - 测试501MB文件应该被拒绝"
echo ""

# 显示当前容器信息
echo "📦 当前服务信息:"
echo "==============="
echo ""
docker ps | grep jar-decompiler-service
echo ""

echo "🌐 访问地址:"
echo "- Web界面: http://localhost:8080"
echo "- 健康检查: http://localhost:8080/api/health"
echo ""

echo "🔍 日志查看:"
echo "docker logs jar-decompiler-service"
echo ""

echo "✅ 文件上传大小限制修复完成！"
echo "现在可以上传最大500MB的JAR文件了。"
