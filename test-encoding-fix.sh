#!/bin/bash

echo "🔧 测试反编译中文注释修复效果"
echo "=============================="
echo ""

# 检查测试JAR文件是否存在
test_jar="test-chinese-comments/target/chinese-comment-test-1.0.0.jar"
if [ ! -f "$test_jar" ]; then
    echo "❌ 测试JAR文件不存在: $test_jar"
    exit 1
fi

echo "✅ 找到测试JAR文件: $test_jar"
echo "📊 文件信息:"
ls -lh "$test_jar"
echo ""

# 检查JAR文件内容
echo "📋 JAR文件内容:"
jar -tf "$test_jar"
echo ""

# 使用CFR直接测试反编译
echo "🔍 使用CFR直接测试反编译..."
temp_dir=$(mktemp -d)
echo "临时目录: $temp_dir"

# 检查CFR是否可用
cfr_jar=$(find ~/.m2/repository -name "cfr-*.jar" 2>/dev/null | head -1)
if [ -z "$cfr_jar" ]; then
    echo "⚠️  CFR JAR文件未找到，尝试从Maven仓库获取..."
    # 尝试从target目录找到CFR
    cfr_jar=$(find target -name "cfr-*.jar" 2>/dev/null | head -1)
fi

if [ -n "$cfr_jar" ]; then
    echo "✅ 找到CFR: $cfr_jar"
    
    # 使用CFR反编译
    java -jar "$cfr_jar" \
        --outputdir "$temp_dir" \
        --outputencoding UTF-8 \
        --charsetname UTF-8 \
        --comments true \
        --caseinsensitivefs true \
        "$test_jar"
    
    echo ""
    echo "📁 反编译结果:"
    find "$temp_dir" -name "*.java" -exec echo "文件: {}" \; -exec head -20 {} \; -exec echo "---" \;
    
else
    echo "⚠️  CFR JAR文件未找到，跳过直接测试"
fi

echo ""
echo "🧹 清理临时文件..."
rm -rf "$temp_dir"

echo ""
echo "📋 修复内容总结:"
echo "1. ✅ 在CFR配置中添加了UTF-8编码选项"
echo "2. ✅ 在ZIP文件创建时使用UTF-8编码"
echo "3. ✅ 在配置文件中添加了字符编码设置"
echo "4. ✅ 在Docker配置中添加了编码环境变量"
echo ""
echo "🔧 主要修复点:"
echo "- CFR选项: outputencoding=UTF-8, charsetname=UTF-8"
echo "- ZIP编码: StandardCharsets.UTF-8"
echo "- JVM参数: -Dfile.encoding=UTF-8 -Dsun.jnu.encoding=UTF-8"
echo "- 环境变量: LANG=C.UTF-8, LC_ALL=C.UTF-8"
echo ""
echo "🚀 要测试完整效果，请:"
echo "1. 重新构建Docker镜像: docker build -f Dockerfile.simple -t jar-decompiler-service:1.0.0 ."
echo "2. 启动服务: docker run -d --name jar-decompiler-service -p 8080:8080 jar-decompiler-service:1.0.0"
echo "3. 访问 http://localhost:8080 并上传测试JAR文件"
echo "4. 检查反编译结果中的中文注释是否正确显示"
