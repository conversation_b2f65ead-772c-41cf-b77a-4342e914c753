# 🚀 手动推送到GitHub指南

由于遇到SSL连接问题，这里提供几种手动解决方案。

## 🔧 快速解决方案

### 方案1: 临时禁用SSL验证 (最快)

```bash
# 进入项目目录
cd /mnt/c/workspace/cvt

# 临时禁用SSL验证
git config --global http.sslVerify false

# 初始化Git仓库
git init

# 添加远程仓库
git remote add origin https://github.com/huruifeng03/jar2resource.git

# 添加所有文件
git add .

# 创建提交
git commit -m "🎉 JAR反编译服务完整版

✨ 功能特性:
- 支持JAR文件上传和反编译
- 使用CFR反编译器
- 完整的项目结构展示
- ZIP文件下载功能
- 国际化支持(英语/中文/德语/法语)
- 响应式Web界面
- RESTful API接口

🔧 技术栈:
- Spring Boot 3.2.5
- Thymeleaf模板引擎
- Bootstrap 5前端框架
- CFR反编译器
- Maven构建工具

🌐 国际化:
- 支持4种语言切换
- Unicode编码修复
- 完整的本地化体验"

# 推送到GitHub
git push -u origin main
```

### 方案2: 使用SSH连接 (推荐)

```bash
# 生成SSH密钥 (如果没有)
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

# 显示公钥
cat ~/.ssh/id_rsa.pub
```

**然后:**
1. 复制显示的SSH公钥
2. 访问 https://github.com/settings/keys
3. 点击 "New SSH key"
4. 粘贴公钥并保存

```bash
# 切换到SSH连接
git remote set-<NAME_EMAIL>:huruifeng03/jar2resource.git

# 推送
git push -u origin main
```

### 方案3: 使用GitHub Desktop (图形界面)

1. 下载并安装 [GitHub Desktop](https://desktop.github.com/)
2. 登录您的GitHub账户
3. 选择 "Add an Existing Repository from your Hard Drive"
4. 选择项目目录 `/mnt/c/workspace/cvt`
5. 点击 "Publish repository"

## 🔍 问题诊断

如果上述方案都不工作，运行诊断脚本：

```bash
chmod +x fix-git-ssl.sh
./fix-git-ssl.sh
```

## 📋 需要推送的文件清单

确保以下文件都在项目目录中：

### 核心代码文件
- `pom.xml` - Maven配置
- `src/main/java/com/jardecompiler/` - Java源代码
- `src/main/resources/` - 资源文件和模板
- `src/test/` - 测试文件

### 配置和资源文件
- `src/main/resources/application.properties` - 应用配置
- `src/main/resources/messages*.properties` - 国际化资源 (4个文件)
- `src/main/resources/templates/` - HTML模板 (2个文件)

### 文档文件
- `README.md` - 项目说明
- `PROJECT_SUMMARY.md` - 项目总结
- `UPDATES_SUMMARY.md` - 更新总结
- `ENCODING_FIX_SUMMARY.md` - 编码修复总结
- `MANUAL_PUSH_GUIDE.md` - 本文件

### 测试和工具文件
- `test-jar/` - 测试用JAR项目
- `*.sh` - 测试和部署脚本
- `.gitignore` - Git忽略文件

## 🎯 推送后的验证

推送成功后，访问以下链接验证：

1. **仓库主页**: https://github.com/huruifeng03/jar2resource
2. **检查文件**: 确保所有源代码文件都已上传
3. **查看README**: 确保项目描述正确显示
4. **检查提交历史**: 确认提交信息完整

## 🔐 安全建议

1. **立即更改GitHub密码** (因为之前在聊天中暴露了)
2. **启用两步验证**: GitHub Settings > Security
3. **使用Personal Access Token**: 代替密码进行认证
4. **定期轮换SSH密钥**: 提高安全性

## 📞 如果仍有问题

1. **检查网络连接**: 确保可以访问GitHub
2. **尝试不同网络**: 如手机热点
3. **联系网络管理员**: 如果在企业网络环境
4. **使用GitHub CLI**: `gh repo create` 命令
5. **直接在GitHub网页上传**: 作为最后手段

## 🎉 成功推送后

推送成功后，您可以：

1. **设置仓库描述**: 在GitHub仓库页面添加描述
2. **添加标签**: 如 `java`, `spring-boot`, `decompiler`, `jar`
3. **创建Release**: 发布第一个版本
4. **设置GitHub Pages**: 如果需要项目网站
5. **配置CI/CD**: 使用GitHub Actions

---

💡 **提示**: 如果SSL问题持续存在，这通常是网络环境或企业防火墙导致的。SSH方式通常是最可靠的解决方案。
